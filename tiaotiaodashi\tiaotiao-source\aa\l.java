package aa;

import java.util.Arrays;
import ta.n;
import ta.q;
import ta.r;
import u8.q1;
import va.p0;

public abstract class l extends f {
  public byte[] j;
  
  public volatile boolean k;
  
  public l(n paramn, r paramr, int paramInt1, q1 paramq1, int paramInt2, Object paramObject, byte[] paramArrayOfbyte) {
    super(paramn, paramr, paramInt1, paramq1, paramInt2, paramObject, -9223372036854775807L, -9223372036854775807L);
    byte[] arrayOfByte;
    if (paramArrayOfbyte == null) {
      arrayOfByte = p0.f;
    } else {
      arrayOfByte = paramArrayOfbyte;
    } 
    this.j = arrayOfByte;
  }
  
  public final void a() {
    try {
      this.i.e(this.b);
      int i = 0;
      int j = 0;
      while (i != -1 && !this.k) {
        i(j);
        int k = this.i.read(this.j, j, 16384);
        i = k;
        if (k != -1) {
          j += k;
          i = k;
        } 
      } 
      if (!this.k)
        g(this.j, j); 
      return;
    } finally {
      q.a((n)this.i);
    } 
  }
  
  public final void b() {
    this.k = true;
  }
  
  public abstract void g(byte[] paramArrayOfbyte, int paramInt);
  
  public byte[] h() {
    return this.j;
  }
  
  public final void i(int paramInt) {
    byte[] arrayOfByte = this.j;
    if (arrayOfByte.length < paramInt + 16384)
      this.j = Arrays.copyOf(arrayOfByte, arrayOfByte.length + 16384); 
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\aa\l.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */