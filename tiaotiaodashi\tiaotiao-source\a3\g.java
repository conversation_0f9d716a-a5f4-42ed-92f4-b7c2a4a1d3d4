package a3;

import android.graphics.Bitmap;
import i3.d0;
import java.nio.ByteBuffer;
import java.util.ArrayDeque;
import l2.p;
import o2.e0;
import r2.f;
import s2.k;
import s2.n1;
import s2.p2;

public class g extends k {
  public int A;
  
  public p B;
  
  public c C;
  
  public f D;
  
  public e E;
  
  public Bitmap F;
  
  public boolean G;
  
  public b H;
  
  public b I;
  
  public int J;
  
  public final c.a r;
  
  public final f s;
  
  public final ArrayDeque<a> t;
  
  public boolean u;
  
  public boolean v;
  
  public a w;
  
  public long x;
  
  public long y;
  
  public int z;
  
  public g(c.a parama, e parame) {
    super(4);
    this.r = parama;
    this.E = v0(parame);
    this.s = f.x();
    this.w = a.c;
    this.t = new ArrayDeque<a>();
    this.y = -9223372036854775807L;
    this.x = -9223372036854775807L;
    this.z = 0;
    this.A = 1;
  }
  
  public static e v0(e parame) {
    e e1 = parame;
    if (parame == null)
      e1 = e.a; 
    return e1;
  }
  
  public final void A0(long paramLong) {
    this.x = paramLong;
    while (!this.t.isEmpty() && paramLong >= ((a)this.t.peek()).a)
      this.w = this.t.removeFirst(); 
  }
  
  public boolean B0(long paramLong1, long paramLong2, Bitmap paramBitmap, long paramLong3) {
    if (E0() || paramLong3 - paramLong1 < 30000L) {
      this.E.b(paramLong3 - this.w.b, paramBitmap);
      return true;
    } 
    return false;
  }
  
  public final void C0() {
    this.D = null;
    this.z = 0;
    this.y = -9223372036854775807L;
    c c1 = this.C;
    if (c1 != null) {
      c1.release();
      this.C = null;
    } 
  }
  
  public final void D0(e parame) {
    this.E = v0(parame);
  }
  
  public final boolean E0() {
    boolean bool;
    if (d() == 2) {
      bool = true;
    } else {
      bool = false;
    } 
    int i = this.A;
    if (i != 0) {
      if (i != 1) {
        if (i == 3)
          return false; 
        throw new IllegalStateException();
      } 
      return true;
    } 
    return bool;
  }
  
  public boolean a() {
    return this.v;
  }
  
  public boolean b() {
    int i = this.A;
    return (i == 3 || (i == 0 && this.G));
  }
  
  public int c(p paramp) {
    return this.r.c(paramp);
  }
  
  public void d0() {
    this.B = null;
    this.w = a.c;
    this.t.clear();
    C0();
    this.E.a();
  }
  
  public void e0(boolean paramBoolean1, boolean paramBoolean2) {
    this.A = paramBoolean2;
  }
  
  public void g0(long paramLong, boolean paramBoolean) {
    y0(1);
    this.v = false;
    this.u = false;
    this.F = null;
    this.H = null;
    this.I = null;
    this.G = false;
    this.D = null;
    c c1 = this.C;
    if (c1 != null)
      c1.flush(); 
    this.t.clear();
  }
  
  public String getName() {
    return "ImageRenderer";
  }
  
  public void h(long paramLong1, long paramLong2) {
    if (this.v)
      return; 
    if (this.B == null) {
      n1 n1 = X();
      this.s.l();
      int i = o0(n1, this.s, 2);
      if (i == -5) {
        this.B = (p)o2.a.i(n1.b);
        w0();
      } else {
        if (i == -4) {
          o2.a.g(this.s.o());
          this.u = true;
          this.v = true;
        } 
        return;
      } 
    } 
    try {
      e0.a("drainAndFeedDecoder");
      while (t0(paramLong1, paramLong2));
      while (u0(paramLong1));
      e0.b();
      return;
    } catch (d d) {
      throw T(d, null, 4003);
    } 
  }
  
  public void h0() {
    C0();
  }
  
  public void j0() {
    C0();
    y0(1);
  }
  
  public void m0(p[] paramArrayOfp, long paramLong1, long paramLong2, d0.b paramb) {
    // Byte code:
    //   0: aload_0
    //   1: aload_1
    //   2: lload_2
    //   3: lload #4
    //   5: aload #6
    //   7: invokespecial m0 : ([Ll2/p;JJLi3/d0$b;)V
    //   10: aload_0
    //   11: getfield w : La3/g$a;
    //   14: getfield b : J
    //   17: ldc2_w -9223372036854775807
    //   20: lcmp
    //   21: ifeq -> 96
    //   24: aload_0
    //   25: getfield t : Ljava/util/ArrayDeque;
    //   28: invokevirtual isEmpty : ()Z
    //   31: ifeq -> 72
    //   34: aload_0
    //   35: getfield y : J
    //   38: lstore #7
    //   40: lload #7
    //   42: ldc2_w -9223372036854775807
    //   45: lcmp
    //   46: ifeq -> 96
    //   49: aload_0
    //   50: getfield x : J
    //   53: lstore_2
    //   54: lload_2
    //   55: ldc2_w -9223372036854775807
    //   58: lcmp
    //   59: ifeq -> 72
    //   62: lload_2
    //   63: lload #7
    //   65: lcmp
    //   66: iflt -> 72
    //   69: goto -> 96
    //   72: aload_0
    //   73: getfield t : Ljava/util/ArrayDeque;
    //   76: new a3/g$a
    //   79: dup
    //   80: aload_0
    //   81: getfield y : J
    //   84: lload #4
    //   86: invokespecial <init> : (JJ)V
    //   89: invokevirtual add : (Ljava/lang/Object;)Z
    //   92: pop
    //   93: goto -> 112
    //   96: aload_0
    //   97: new a3/g$a
    //   100: dup
    //   101: ldc2_w -9223372036854775807
    //   104: lload #4
    //   106: invokespecial <init> : (JJ)V
    //   109: putfield w : La3/g$a;
    //   112: return
  }
  
  public final boolean r0(p paramp) {
    int i = this.r.c(paramp);
    return (i == p2.u(4) || i == p2.u(3));
  }
  
  public final Bitmap s0(int paramInt) {
    o2.a.i(this.F);
    int j = this.F.getWidth() / ((p)o2.a.i(this.B)).I;
    int i = this.F.getHeight() / ((p)o2.a.i(this.B)).J;
    int n = this.B.I;
    int m = paramInt / n;
    return Bitmap.createBitmap(this.F, paramInt % n * j, m * i, j, i);
  }
  
  public final boolean t0(long paramLong1, long paramLong2) {
    if (this.F != null && this.H == null)
      return false; 
    if (this.A == 0 && d() != 2)
      return false; 
    if (this.F == null) {
      o2.a.i(this.C);
      f f1 = this.C.a();
      if (f1 == null)
        return false; 
      if (((f)o2.a.i(f1)).o()) {
        if (this.z == 3) {
          C0();
          o2.a.i(this.B);
          w0();
        } else {
          ((f)o2.a.i(f1)).t();
          if (this.t.isEmpty())
            this.v = true; 
        } 
        return false;
      } 
      o2.a.j(f1.e, "Non-EOS buffer came back from the decoder without bitmap.");
      this.F = f1.e;
      ((f)o2.a.i(f1)).t();
    } 
    if (this.G && this.F != null && this.H != null) {
      o2.a.i(this.B);
      p p1 = this.B;
      int i = p1.I;
      if ((i != 1 || p1.J != 1) && i != -1 && p1.J != -1) {
        i = 1;
      } else {
        i = 0;
      } 
      if (!this.H.d()) {
        Bitmap bitmap;
        b b1 = this.H;
        if (i != 0) {
          bitmap = s0(b1.c());
        } else {
          bitmap = (Bitmap)o2.a.i(this.F);
        } 
        b1.e(bitmap);
      } 
      if (!B0(paramLong1, paramLong2, (Bitmap)o2.a.i(this.H.b()), this.H.a()))
        return false; 
      A0(((b)o2.a.i(this.H)).a());
      this.A = 3;
      if (i == 0 || ((b)o2.a.i(this.H)).c() == ((p)o2.a.i(this.B)).J * ((p)o2.a.i(this.B)).I - 1)
        this.F = null; 
      this.H = this.I;
      this.I = null;
      return true;
    } 
    return false;
  }
  
  public final boolean u0(long paramLong) {
    if (this.G && this.H != null)
      return false; 
    n1 n1 = X();
    c c1 = this.C;
    if (c1 == null || this.z == 3 || this.u)
      return false; 
    if (this.D == null) {
      f f1 = (f)c1.c();
      this.D = f1;
      if (f1 == null)
        return false; 
    } 
    if (this.z == 2) {
      o2.a.i(this.D);
      this.D.s(4);
      ((c)o2.a.i(this.C)).e(this.D);
      this.D = null;
      this.z = 3;
      return false;
    } 
    int i = o0(n1, this.D, 0);
    if (i != -5) {
      if (i != -4) {
        if (i == -3)
          return false; 
        throw new IllegalStateException();
      } 
      this.D.v();
      if (((ByteBuffer)o2.a.i(this.D.d)).remaining() > 0 || ((f)o2.a.i(this.D)).o()) {
        i = 1;
      } else {
        i = 0;
      } 
      if (i != 0) {
        ((c)o2.a.i(this.C)).e((f)o2.a.i(this.D));
        this.J = 0;
      } 
      z0(paramLong, (f)o2.a.i(this.D));
      if (((f)o2.a.i(this.D)).o()) {
        this.u = true;
        this.D = null;
        return false;
      } 
      this.y = Math.max(this.y, ((f)o2.a.i(this.D)).f);
      if (i != 0) {
        this.D = null;
      } else {
        ((f)o2.a.i(this.D)).l();
      } 
      return this.G ^ true;
    } 
    this.B = (p)o2.a.i(n1.b);
    this.z = 2;
    return true;
  }
  
  public void w(int paramInt, Object paramObject) {
    if (paramInt != 15) {
      super.w(paramInt, paramObject);
    } else {
      if (paramObject instanceof e) {
        paramObject = paramObject;
      } else {
        paramObject = null;
      } 
      D0((e)paramObject);
    } 
  }
  
  public final void w0() {
    if (r0(this.B)) {
      c c1 = this.C;
      if (c1 != null)
        c1.release(); 
      this.C = this.r.d();
      return;
    } 
    throw T(new d("Provided decoder factory can't create decoder for format."), this.B, 4005);
  }
  
  public final boolean x0(b paramb) {
    int i = ((p)o2.a.i(this.B)).I;
    boolean bool2 = true;
    boolean bool1 = bool2;
    if (i != -1) {
      bool1 = bool2;
      if (this.B.J != -1)
        if (paramb.c() == ((p)o2.a.i(this.B)).J * this.B.I - 1) {
          bool1 = bool2;
        } else {
          bool1 = false;
        }  
    } 
    return bool1;
  }
  
  public final void y0(int paramInt) {
    this.A = Math.min(this.A, paramInt);
  }
  
  public final void z0(long paramLong, f paramf) {
    boolean bool = paramf.o();
    boolean bool1 = true;
    if (bool) {
      this.G = true;
      return;
    } 
    b b1 = new b(this.J, paramf.f);
    this.I = b1;
    this.J++;
    if (!this.G) {
      boolean bool2;
      boolean bool3;
      long l = b1.a();
      if (l - 30000L <= paramLong && paramLong <= 30000L + l) {
        bool2 = true;
      } else {
        bool2 = false;
      } 
      b1 = this.H;
      if (b1 != null && b1.a() <= paramLong && paramLong < l) {
        bool3 = true;
      } else {
        bool3 = false;
      } 
      boolean bool4 = x0((b)o2.a.i(this.I));
      bool = bool1;
      if (!bool2) {
        bool = bool1;
        if (!bool3)
          if (bool4) {
            bool = bool1;
          } else {
            bool = false;
          }  
      } 
      this.G = bool;
      if (bool3 && !bool2)
        return; 
    } 
    this.H = this.I;
    this.I = null;
  }
  
  public static final class a {
    public static final a c = new a(-9223372036854775807L, -9223372036854775807L);
    
    public final long a;
    
    public final long b;
    
    public a(long param1Long1, long param1Long2) {
      this.a = param1Long1;
      this.b = param1Long2;
    }
  }
  
  public static class b {
    public final int a;
    
    public final long b;
    
    public Bitmap c;
    
    public b(int param1Int, long param1Long) {
      this.a = param1Int;
      this.b = param1Long;
    }
    
    public long a() {
      return this.b;
    }
    
    public Bitmap b() {
      return this.c;
    }
    
    public int c() {
      return this.a;
    }
    
    public boolean d() {
      boolean bool;
      if (this.c != null) {
        bool = true;
      } else {
        bool = false;
      } 
      return bool;
    }
    
    public void e(Bitmap param1Bitmap) {
      this.c = param1Bitmap;
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a3\g.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */