package android.support.v4.media;

import android.os.Parcel;
import android.os.Parcelable;

public class MediaBrowserCompat$MediaItem implements Parcelable {
  public static final Parcelable.Creator<MediaBrowserCompat$MediaItem> CREATOR = new a();
  
  public final int a;
  
  public final MediaDescriptionCompat b;
  
  public MediaBrowserCompat$MediaItem(Parcel paramParcel) {
    this.a = paramParcel.readInt();
    this.b = (MediaDescriptionCompat)MediaDescriptionCompat.CREATOR.createFromParcel(paramParcel);
  }
  
  public int describeContents() {
    return 0;
  }
  
  public String toString() {
    StringBuilder stringBuilder = new StringBuilder("MediaItem{");
    stringBuilder.append("mFlags=");
    stringBuilder.append(this.a);
    stringBuilder.append(", mDescription=");
    stringBuilder.append(this.b);
    stringBuilder.append('}');
    return stringBuilder.toString();
  }
  
  public void writeToParcel(Parcel paramParcel, int paramInt) {
    paramParcel.writeInt(this.a);
    this.b.writeToParcel(paramParcel, paramInt);
  }
  
  public class a implements Parcelable.Creator<MediaBrowserCompat$MediaItem> {
    public MediaBrowserCompat$MediaItem a(Parcel param1Parcel) {
      return new MediaBrowserCompat$MediaItem(param1Parcel);
    }
    
    public MediaBrowserCompat$MediaItem[] b(int param1Int) {
      return new MediaBrowserCompat$MediaItem[param1Int];
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\android\support\v4\media\MediaBrowserCompat$MediaItem.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */