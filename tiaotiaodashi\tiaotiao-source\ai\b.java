package ai;

import android.os.Build;
import android.security.NetworkSecurityPolicy;
import bi.d;
import bi.h;
import bi.i;
import bi.j;
import bi.k;
import bi.l;
import bi.m;
import bi.n;
import di.c;
import di.e;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.security.cert.TrustAnchor;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.X509TrustManager;
import lg.l;
import rh.x;
import xg.g;
import xg.m;

public final class b extends j {
  public static final a f = new a(null);
  
  public static final boolean g;
  
  public final List<m> d;
  
  public final j e;
  
  static {
    boolean bool1 = j.a.h();
    boolean bool = false;
    if (bool1 && Build.VERSION.SDK_INT < 30)
      bool = true; 
    g = bool;
  }
  
  public b() {
    list = l.l((Object[])new m[] { n.a.b(n.j, null, 1, null), (m)new l(h.f.d()), (m)new l(k.a.a()), (m)new l(i.a.a()) });
    ArrayList<List> arrayList = new ArrayList();
    for (List list : list) {
      if (((m)list).b())
        arrayList.add(list); 
    } 
    this.d = (List)arrayList;
    this.e = j.d.a();
  }
  
  public c c(X509TrustManager paramX509TrustManager) {
    d d1;
    c c;
    m.e(paramX509TrustManager, "trustManager");
    d d2 = d.d.a(paramX509TrustManager);
    if (d2 != null) {
      d1 = d2;
    } else {
      c = super.c((X509TrustManager)d1);
    } 
    return c;
  }
  
  public e d(X509TrustManager paramX509TrustManager) {
    e e;
    m.e(paramX509TrustManager, "trustManager");
    try {
      Method method = paramX509TrustManager.getClass().getDeclaredMethod("findTrustAnchorByIssuerAndSignature", new Class[] { X509Certificate.class });
      method.setAccessible(true);
      b b1 = new b();
      m.d(method, "method");
      this(paramX509TrustManager, method);
      e = b1;
    } catch (NoSuchMethodException noSuchMethodException) {
      e = super.d((X509TrustManager)e);
    } 
    return e;
  }
  
  public void e(SSLSocket paramSSLSocket, String paramString, List<x> paramList) {
    // Byte code:
    //   0: aload_1
    //   1: ldc 'sslSocket'
    //   3: invokestatic e : (Ljava/lang/Object;Ljava/lang/String;)V
    //   6: aload_3
    //   7: ldc 'protocols'
    //   9: invokestatic e : (Ljava/lang/Object;Ljava/lang/String;)V
    //   12: aload_0
    //   13: getfield d : Ljava/util/List;
    //   16: checkcast java/lang/Iterable
    //   19: invokeinterface iterator : ()Ljava/util/Iterator;
    //   24: astore #5
    //   26: aload #5
    //   28: invokeinterface hasNext : ()Z
    //   33: ifeq -> 62
    //   36: aload #5
    //   38: invokeinterface next : ()Ljava/lang/Object;
    //   43: astore #4
    //   45: aload #4
    //   47: checkcast bi/m
    //   50: aload_1
    //   51: invokeinterface a : (Ljavax/net/ssl/SSLSocket;)Z
    //   56: ifeq -> 26
    //   59: goto -> 65
    //   62: aconst_null
    //   63: astore #4
    //   65: aload #4
    //   67: checkcast bi/m
    //   70: astore #4
    //   72: aload #4
    //   74: ifnull -> 87
    //   77: aload #4
    //   79: aload_1
    //   80: aload_2
    //   81: aload_3
    //   82: invokeinterface d : (Ljavax/net/ssl/SSLSocket;Ljava/lang/String;Ljava/util/List;)V
    //   87: return
  }
  
  public void f(Socket paramSocket, InetSocketAddress paramInetSocketAddress, int paramInt) {
    m.e(paramSocket, "socket");
    m.e(paramInetSocketAddress, "address");
    try {
      paramSocket.connect(paramInetSocketAddress, paramInt);
      return;
    } catch (ClassCastException classCastException) {
      if (Build.VERSION.SDK_INT == 26)
        throw new IOException("Exception in connect", classCastException); 
      throw classCastException;
    } 
  }
  
  public String g(SSLSocket paramSSLSocket) {
    String str;
    m m2;
    m.e(paramSSLSocket, "sslSocket");
    Iterator<m> iterator = this.d.iterator();
    while (true) {
      boolean bool = iterator.hasNext();
      m2 = null;
      if (bool) {
        m m = (m)iterator.next();
        if (((m)m).a(paramSSLSocket))
          break; 
        continue;
      } 
      m1 = null;
      break;
    } 
    m m3 = m1;
    m m1 = m2;
    if (m3 != null)
      str = m3.c(paramSSLSocket); 
    return str;
  }
  
  public Object h(String paramString) {
    m.e(paramString, "closer");
    return this.e.a(paramString);
  }
  
  public boolean i(String paramString) {
    m.e(paramString, "hostname");
    return NetworkSecurityPolicy.getInstance().isCleartextTrafficPermitted(paramString);
  }
  
  public void l(String paramString, Object paramObject) {
    m.e(paramString, "message");
    if (!this.e.b(paramObject))
      j.k(this, paramString, 5, null, 4, null); 
  }
  
  public static final class a {
    public a() {}
    
    public final j a() {
      j j;
      if (b()) {
        j = new b();
      } else {
        j = null;
      } 
      return j;
    }
    
    public final boolean b() {
      return b.p();
    }
  }
  
  public static final class b implements e {
    public final X509TrustManager a;
    
    public final Method b;
    
    public b(X509TrustManager param1X509TrustManager, Method param1Method) {
      this.a = param1X509TrustManager;
      this.b = param1Method;
    }
    
    public X509Certificate a(X509Certificate param1X509Certificate) {
      m.e(param1X509Certificate, "cert");
      try {
        null = this.b.invoke(this.a, new Object[] { param1X509Certificate });
        m.c(null, "null cannot be cast to non-null type java.security.cert.TrustAnchor");
        return ((TrustAnchor)null).getTrustedCert();
      } catch (IllegalAccessException illegalAccessException) {
        throw new AssertionError("unable to get issues and signature", illegalAccessException);
      } catch (InvocationTargetException null) {
        return null;
      } 
    }
    
    public boolean equals(Object param1Object) {
      if (this == param1Object)
        return true; 
      if (!(param1Object instanceof b))
        return false; 
      param1Object = param1Object;
      return !m.a(this.a, ((b)param1Object).a) ? false : (!!m.a(this.b, ((b)param1Object).b));
    }
    
    public int hashCode() {
      return this.a.hashCode() * 31 + this.b.hashCode();
    }
    
    public String toString() {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("CustomTrustRootIndex(trustManager=");
      stringBuilder.append(this.a);
      stringBuilder.append(", findByIssuerAndSignatureMethod=");
      stringBuilder.append(this.b);
      stringBuilder.append(')');
      return stringBuilder.toString();
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ai\b.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */