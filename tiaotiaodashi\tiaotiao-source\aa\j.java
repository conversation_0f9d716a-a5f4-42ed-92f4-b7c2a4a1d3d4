package aa;

import java.util.List;
import ta.f0;
import u8.e3;

public interface j {
  void a();
  
  long l(long paramLong, e3 parame3);
  
  int m(long paramLong, List<? extends n> paramList);
  
  void n(f paramf);
  
  void o(long paramLong1, long paramLong2, List<? extends n> paramList, h paramh);
  
  boolean p(long paramLong, f paramf, List<? extends n> paramList);
  
  boolean q(f paramf, boolean paramBoolean, f0.c paramc, f0 paramf0);
  
  void release();
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\aa\j.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */