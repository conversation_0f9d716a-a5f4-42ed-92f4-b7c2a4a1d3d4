package ai;

import java.security.KeyStore;
import java.security.Provider;
import java.util.Arrays;
import java.util.List;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import org.bouncycastle.jsse.BCSSLParameters;
import org.bouncycastle.jsse.BCSSLSocket;
import org.bouncycastle.jsse.provider.BouncyCastleJsseProvider;
import rh.x;
import xg.g;
import xg.m;

public final class c extends j {
  public static final a e;
  
  public static final boolean f;
  
  public final Provider d = (Provider)new BouncyCastleJsseProvider();
  
  static {
    a a1 = new a(null);
    e = a1;
    boolean bool = false;
    try {
      Class.forName("org.bouncycastle.jsse.provider.BouncyCastleJsseProvider", false, a1.getClass().getClassLoader());
      bool = true;
    } catch (ClassNotFoundException classNotFoundException) {}
    f = bool;
  }
  
  public c() {}
  
  public void e(SSLSocket paramSSLSocket, String paramString, List<x> paramList) {
    BCSSLParameters bCSSLParameters;
    BCSSLSocket bCSSLSocket;
    m.e(paramSSLSocket, "sslSocket");
    m.e(paramList, "protocols");
    if (paramSSLSocket instanceof BCSSLSocket) {
      bCSSLSocket = (BCSSLSocket)paramSSLSocket;
      bCSSLParameters = bCSSLSocket.getParameters();
      bCSSLParameters.setApplicationProtocols(j.a.b(paramList).<String>toArray(new String[0]));
      bCSSLSocket.setParameters(bCSSLParameters);
    } else {
      super.e((SSLSocket)bCSSLParameters, (String)bCSSLSocket, paramList);
    } 
  }
  
  public String g(SSLSocket paramSSLSocket) {
    String str;
    m.e(paramSSLSocket, "sslSocket");
    if (paramSSLSocket instanceof BCSSLSocket) {
      boolean bool;
      str = ((BCSSLSocket)paramSSLSocket).getApplicationProtocol();
      if (str == null) {
        bool = true;
      } else {
        bool = m.a(str, "");
      } 
      if (bool)
        str = null; 
    } else {
      str = super.g((SSLSocket)str);
    } 
    return str;
  }
  
  public SSLContext m() {
    SSLContext sSLContext = SSLContext.getInstance("TLS", this.d);
    m.d(sSLContext, "getInstance(\"TLS\", provider)");
    return sSLContext;
  }
  
  public X509TrustManager o() {
    TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance("PKIX", "BCJSSE");
    trustManagerFactory.init((KeyStore)null);
    TrustManager[] arrayOfTrustManager = trustManagerFactory.getTrustManagers();
    m.b(arrayOfTrustManager);
    int i = arrayOfTrustManager.length;
    boolean bool = true;
    if (i != 1 || !(arrayOfTrustManager[0] instanceof X509TrustManager))
      bool = false; 
    if (bool) {
      TrustManager trustManager = arrayOfTrustManager[0];
      m.c(trustManager, "null cannot be cast to non-null type javax.net.ssl.X509TrustManager");
      return (X509TrustManager)trustManager;
    } 
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("Unexpected default trust managers: ");
    String str = Arrays.toString((Object[])arrayOfTrustManager);
    m.d(str, "toString(this)");
    stringBuilder.append(str);
    throw new IllegalStateException(stringBuilder.toString().toString());
  }
  
  public static final class a {
    public a() {}
    
    public final c a() {
      boolean bool = b();
      c c = null;
      if (bool)
        c = new c(null); 
      return c;
    }
    
    public final boolean b() {
      return c.p();
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ai\c.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */