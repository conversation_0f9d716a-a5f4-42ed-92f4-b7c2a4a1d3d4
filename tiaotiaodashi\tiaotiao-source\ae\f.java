package ae;

import qg.a;
import qg.b;

public enum f {
  DENIED, GRANTED, PERMANENTLY_DENIED;
  
  private static final a $ENTRIES;
  
  private static final f[] $VALUES;
  
  static {
    DENIED = new f("DENIED", 1);
    PERMANENTLY_DENIED = new f("PERMANENTLY_DENIED", 2);
    f[] arrayOfF = a();
    $VALUES = arrayOfF;
    $ENTRIES = b.a((Enum[])arrayOfF);
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ae\f.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */