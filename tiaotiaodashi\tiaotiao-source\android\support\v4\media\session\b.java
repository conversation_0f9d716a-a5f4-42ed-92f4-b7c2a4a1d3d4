package android.support.v4.media.session;

import android.app.PendingIntent;
import android.net.Uri;
import android.os.Binder;
import android.os.Bundle;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.support.v4.media.MediaDescriptionCompat;
import android.support.v4.media.MediaMetadataCompat;
import android.support.v4.media.RatingCompat;
import android.text.TextUtils;
import android.view.KeyEvent;
import java.util.List;

public interface b extends IInterface {
  void B0(int paramInt);
  
  void C(MediaDescriptionCompat paramMediaDescriptionCompat);
  
  boolean C0();
  
  boolean D();
  
  void E(MediaDescriptionCompat paramMediaDescriptionCompat);
  
  void F0(String paramString, Bundle paramBundle, MediaSessionCompat.ResultReceiverWrapper paramResultReceiverWrapper);
  
  PendingIntent G();
  
  void G0();
  
  int I();
  
  void K(String paramString, Bundle paramBundle);
  
  void L0(long paramLong);
  
  void M0(boolean paramBoolean);
  
  ParcelableVolumeInfo N0();
  
  String R0();
  
  CharSequence W();
  
  void Z(String paramString, Bundle paramBundle);
  
  Bundle a0();
  
  void b0(a parama);
  
  void c0(String paramString, Bundle paramBundle);
  
  void d();
  
  void d0(String paramString, Bundle paramBundle);
  
  void e();
  
  void f0();
  
  PlaybackStateCompat g();
  
  void g0(Uri paramUri, Bundle paramBundle);
  
  Bundle getExtras();
  
  String getTag();
  
  void h();
  
  void j(int paramInt);
  
  MediaMetadataCompat k();
  
  void l(long paramLong);
  
  int m();
  
  void n(float paramFloat);
  
  boolean n0(KeyEvent paramKeyEvent);
  
  void next();
  
  List<MediaSessionCompat.QueueItem> o();
  
  long p();
  
  void previous();
  
  void q0(int paramInt1, int paramInt2, String paramString);
  
  void r(int paramInt);
  
  void r0(RatingCompat paramRatingCompat, Bundle paramBundle);
  
  void s(String paramString, Bundle paramBundle);
  
  void stop();
  
  void t(a parama);
  
  void t0(MediaDescriptionCompat paramMediaDescriptionCompat, int paramInt);
  
  boolean v();
  
  void w(RatingCompat paramRatingCompat);
  
  void w0(boolean paramBoolean);
  
  void y(int paramInt1, int paramInt2, String paramString);
  
  void z(Uri paramUri, Bundle paramBundle);
  
  int z0();
  
  public static abstract class a extends Binder implements b {
    public a() {
      attachInterface(this, "android.support.v4.media.session.IMediaSession");
    }
    
    public static b c(IBinder param1IBinder) {
      if (param1IBinder == null)
        return null; 
      IInterface iInterface = param1IBinder.queryLocalInterface("android.support.v4.media.session.IMediaSession");
      return (iInterface != null && iInterface instanceof b) ? (b)iInterface : new a(param1IBinder);
    }
    
    public static b f() {
      return a.b;
    }
    
    public IBinder asBinder() {
      return (IBinder)this;
    }
    
    public boolean onTransact(int param1Int1, Parcel param1Parcel1, Parcel param1Parcel2, int param1Int2) {
      if (param1Int1 != 1598968902) {
        boolean bool2;
        int i;
        boolean bool1;
        Bundle bundle1;
        CharSequence charSequence;
        List<MediaSessionCompat.QueueItem> list;
        PlaybackStateCompat playbackStateCompat;
        MediaMetadataCompat mediaMetadataCompat;
        ParcelableVolumeInfo parcelableVolumeInfo;
        PendingIntent pendingIntent;
        String str1;
        long l;
        MediaDescriptionCompat mediaDescriptionCompat1;
        Bundle bundle3;
        RatingCompat ratingCompat1;
        Bundle bundle2;
        KeyEvent keyEvent;
        String str3;
        Bundle bundle4;
        String str2;
        MediaSessionCompat.ResultReceiverWrapper resultReceiverWrapper;
        boolean bool4 = false;
        boolean bool3 = false;
        RatingCompat ratingCompat4 = null;
        MediaDescriptionCompat mediaDescriptionCompat3 = null;
        MediaDescriptionCompat mediaDescriptionCompat4 = null;
        Bundle bundle6 = null;
        MediaDescriptionCompat mediaDescriptionCompat2 = null;
        Bundle bundle9 = null;
        Bundle bundle8 = null;
        Bundle bundle10 = null;
        String str5 = null;
        RatingCompat ratingCompat3 = null;
        RatingCompat ratingCompat2 = null;
        Bundle bundle11 = null;
        Bundle bundle5 = null;
        Bundle bundle7 = null;
        switch (param1Int1) {
          default:
            return super.onTransact(param1Int1, param1Parcel1, param1Parcel2, param1Int2);
          case 51:
            param1Parcel1.enforceInterface("android.support.v4.media.session.IMediaSession");
            if (param1Parcel1.readInt() != 0) {
              ratingCompat2 = (RatingCompat)RatingCompat.CREATOR.createFromParcel(param1Parcel1);
            } else {
              ratingCompat2 = null;
            } 
            bundle5 = bundle7;
            if (param1Parcel1.readInt() != 0)
              bundle5 = (Bundle)Bundle.CREATOR.createFromParcel(param1Parcel1); 
            r0(ratingCompat2, bundle5);
            param1Parcel2.writeNoException();
            return true;
          case 50:
            param1Parcel1.enforceInterface("android.support.v4.media.session.IMediaSession");
            bundle1 = a0();
            param1Parcel2.writeNoException();
            if (bundle1 != null) {
              param1Parcel2.writeInt(1);
              bundle1.writeToParcel(param1Parcel2, 1);
            } else {
              param1Parcel2.writeInt(0);
            } 
            return true;
          case 49:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            n(bundle1.readFloat());
            param1Parcel2.writeNoException();
            return true;
          case 48:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            r(bundle1.readInt());
            param1Parcel2.writeNoException();
            return true;
          case 47:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            param1Int1 = z0();
            param1Parcel2.writeNoException();
            param1Parcel2.writeInt(param1Int1);
            return true;
          case 46:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            if (bundle1.readInt() != 0)
              bool3 = true; 
            w0(bool3);
            param1Parcel2.writeNoException();
            return true;
          case 45:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            bool2 = C0();
            param1Parcel2.writeNoException();
            param1Parcel2.writeInt(bool2);
            return true;
          case 44:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            B0(bundle1.readInt());
            param1Parcel2.writeNoException();
            return true;
          case 43:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            ratingCompat2 = ratingCompat4;
            if (bundle1.readInt() != 0)
              mediaDescriptionCompat1 = (MediaDescriptionCompat)MediaDescriptionCompat.CREATOR.createFromParcel((Parcel)bundle1); 
            C(mediaDescriptionCompat1);
            param1Parcel2.writeNoException();
            return true;
          case 42:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            mediaDescriptionCompat1 = mediaDescriptionCompat3;
            if (bundle1.readInt() != 0)
              mediaDescriptionCompat1 = (MediaDescriptionCompat)MediaDescriptionCompat.CREATOR.createFromParcel((Parcel)bundle1); 
            t0(mediaDescriptionCompat1, bundle1.readInt());
            param1Parcel2.writeNoException();
            return true;
          case 41:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            mediaDescriptionCompat1 = mediaDescriptionCompat4;
            if (bundle1.readInt() != 0)
              mediaDescriptionCompat1 = (MediaDescriptionCompat)MediaDescriptionCompat.CREATOR.createFromParcel((Parcel)bundle1); 
            E(mediaDescriptionCompat1);
            param1Parcel2.writeNoException();
            return true;
          case 40:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            bool3 = bool4;
            if (bundle1.readInt() != 0)
              bool3 = true; 
            M0(bool3);
            param1Parcel2.writeNoException();
            return true;
          case 39:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            j(bundle1.readInt());
            param1Parcel2.writeNoException();
            return true;
          case 38:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            bool2 = v();
            param1Parcel2.writeNoException();
            param1Parcel2.writeInt(bool2);
            return true;
          case 37:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            i = m();
            param1Parcel2.writeNoException();
            param1Parcel2.writeInt(i);
            return true;
          case 36:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            if (bundle1.readInt() != 0) {
              Uri uri = (Uri)Uri.CREATOR.createFromParcel((Parcel)bundle1);
            } else {
              mediaDescriptionCompat1 = null;
            } 
            bundle5 = bundle6;
            if (bundle1.readInt() != 0)
              bundle5 = (Bundle)Bundle.CREATOR.createFromParcel((Parcel)bundle1); 
            z((Uri)mediaDescriptionCompat1, bundle5);
            param1Parcel2.writeNoException();
            return true;
          case 35:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            str3 = bundle1.readString();
            mediaDescriptionCompat1 = mediaDescriptionCompat2;
            if (bundle1.readInt() != 0)
              bundle3 = (Bundle)Bundle.CREATOR.createFromParcel((Parcel)bundle1); 
            K(str3, bundle3);
            param1Parcel2.writeNoException();
            return true;
          case 34:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            str3 = bundle1.readString();
            bundle3 = bundle9;
            if (bundle1.readInt() != 0)
              bundle3 = (Bundle)Bundle.CREATOR.createFromParcel((Parcel)bundle1); 
            Z(str3, bundle3);
            param1Parcel2.writeNoException();
            return true;
          case 33:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            e();
            param1Parcel2.writeNoException();
            return true;
          case 32:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            i = I();
            param1Parcel2.writeNoException();
            param1Parcel2.writeInt(i);
            return true;
          case 31:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            bundle1 = getExtras();
            param1Parcel2.writeNoException();
            if (bundle1 != null) {
              param1Parcel2.writeInt(1);
              bundle1.writeToParcel(param1Parcel2, 1);
            } else {
              param1Parcel2.writeInt(0);
            } 
            return true;
          case 30:
            bundle1.enforceInterface("android.support.v4.media.session.IMediaSession");
            charSequence = W();
            param1Parcel2.writeNoException();
            if (charSequence != null) {
              param1Parcel2.writeInt(1);
              TextUtils.writeToParcel(charSequence, param1Parcel2, 1);
            } else {
              param1Parcel2.writeInt(0);
            } 
            return true;
          case 29:
            charSequence.enforceInterface("android.support.v4.media.session.IMediaSession");
            list = o();
            param1Parcel2.writeNoException();
            param1Parcel2.writeTypedList(list);
            return true;
          case 28:
            list.enforceInterface("android.support.v4.media.session.IMediaSession");
            playbackStateCompat = g();
            param1Parcel2.writeNoException();
            if (playbackStateCompat != null) {
              param1Parcel2.writeInt(1);
              playbackStateCompat.writeToParcel(param1Parcel2, 1);
            } else {
              param1Parcel2.writeInt(0);
            } 
            return true;
          case 27:
            playbackStateCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            mediaMetadataCompat = k();
            param1Parcel2.writeNoException();
            if (mediaMetadataCompat != null) {
              param1Parcel2.writeInt(1);
              mediaMetadataCompat.writeToParcel(param1Parcel2, 1);
            } else {
              param1Parcel2.writeInt(0);
            } 
            return true;
          case 26:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            str3 = mediaMetadataCompat.readString();
            bundle3 = bundle8;
            if (mediaMetadataCompat.readInt() != 0)
              bundle3 = (Bundle)Bundle.CREATOR.createFromParcel((Parcel)mediaMetadataCompat); 
            s(str3, bundle3);
            param1Parcel2.writeNoException();
            return true;
          case 25:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            bundle3 = bundle10;
            if (mediaMetadataCompat.readInt() != 0)
              ratingCompat1 = (RatingCompat)RatingCompat.CREATOR.createFromParcel((Parcel)mediaMetadataCompat); 
            w(ratingCompat1);
            param1Parcel2.writeNoException();
            return true;
          case 24:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            l(mediaMetadataCompat.readLong());
            param1Parcel2.writeNoException();
            return true;
          case 23:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            G0();
            param1Parcel2.writeNoException();
            return true;
          case 22:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            f0();
            param1Parcel2.writeNoException();
            return true;
          case 21:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            previous();
            param1Parcel2.writeNoException();
            return true;
          case 20:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            next();
            param1Parcel2.writeNoException();
            return true;
          case 19:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            stop();
            param1Parcel2.writeNoException();
            return true;
          case 18:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            d();
            param1Parcel2.writeNoException();
            return true;
          case 17:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            L0(mediaMetadataCompat.readLong());
            param1Parcel2.writeNoException();
            return true;
          case 16:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            if (mediaMetadataCompat.readInt() != 0) {
              Uri uri = (Uri)Uri.CREATOR.createFromParcel((Parcel)mediaMetadataCompat);
            } else {
              ratingCompat1 = null;
            } 
            str3 = str5;
            if (mediaMetadataCompat.readInt() != 0)
              bundle4 = (Bundle)Bundle.CREATOR.createFromParcel((Parcel)mediaMetadataCompat); 
            g0((Uri)ratingCompat1, bundle4);
            param1Parcel2.writeNoException();
            return true;
          case 15:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            str2 = mediaMetadataCompat.readString();
            ratingCompat1 = ratingCompat3;
            if (mediaMetadataCompat.readInt() != 0)
              bundle2 = (Bundle)Bundle.CREATOR.createFromParcel((Parcel)mediaMetadataCompat); 
            d0(str2, bundle2);
            param1Parcel2.writeNoException();
            return true;
          case 14:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            str2 = mediaMetadataCompat.readString();
            if (mediaMetadataCompat.readInt() != 0)
              bundle2 = (Bundle)Bundle.CREATOR.createFromParcel((Parcel)mediaMetadataCompat); 
            c0(str2, bundle2);
            param1Parcel2.writeNoException();
            return true;
          case 13:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            h();
            param1Parcel2.writeNoException();
            return true;
          case 12:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            y(mediaMetadataCompat.readInt(), mediaMetadataCompat.readInt(), mediaMetadataCompat.readString());
            param1Parcel2.writeNoException();
            return true;
          case 11:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            q0(mediaMetadataCompat.readInt(), mediaMetadataCompat.readInt(), mediaMetadataCompat.readString());
            param1Parcel2.writeNoException();
            return true;
          case 10:
            mediaMetadataCompat.enforceInterface("android.support.v4.media.session.IMediaSession");
            parcelableVolumeInfo = N0();
            param1Parcel2.writeNoException();
            if (parcelableVolumeInfo != null) {
              param1Parcel2.writeInt(1);
              parcelableVolumeInfo.writeToParcel(param1Parcel2, 1);
            } else {
              param1Parcel2.writeInt(0);
            } 
            return true;
          case 9:
            parcelableVolumeInfo.enforceInterface("android.support.v4.media.session.IMediaSession");
            l = p();
            param1Parcel2.writeNoException();
            param1Parcel2.writeLong(l);
            return true;
          case 8:
            parcelableVolumeInfo.enforceInterface("android.support.v4.media.session.IMediaSession");
            pendingIntent = G();
            param1Parcel2.writeNoException();
            if (pendingIntent != null) {
              param1Parcel2.writeInt(1);
              pendingIntent.writeToParcel(param1Parcel2, 1);
            } else {
              param1Parcel2.writeInt(0);
            } 
            return true;
          case 7:
            pendingIntent.enforceInterface("android.support.v4.media.session.IMediaSession");
            str1 = getTag();
            param1Parcel2.writeNoException();
            param1Parcel2.writeString(str1);
            return true;
          case 6:
            str1.enforceInterface("android.support.v4.media.session.IMediaSession");
            str1 = R0();
            param1Parcel2.writeNoException();
            param1Parcel2.writeString(str1);
            return true;
          case 5:
            str1.enforceInterface("android.support.v4.media.session.IMediaSession");
            bool1 = D();
            param1Parcel2.writeNoException();
            param1Parcel2.writeInt(bool1);
            return true;
          case 4:
            str1.enforceInterface("android.support.v4.media.session.IMediaSession");
            b0(a.a.c(str1.readStrongBinder()));
            param1Parcel2.writeNoException();
            return true;
          case 3:
            str1.enforceInterface("android.support.v4.media.session.IMediaSession");
            t(a.a.c(str1.readStrongBinder()));
            param1Parcel2.writeNoException();
            return true;
          case 2:
            str1.enforceInterface("android.support.v4.media.session.IMediaSession");
            bundle2 = bundle11;
            if (str1.readInt() != 0)
              keyEvent = (KeyEvent)KeyEvent.CREATOR.createFromParcel((Parcel)str1); 
            bool1 = n0(keyEvent);
            param1Parcel2.writeNoException();
            param1Parcel2.writeInt(bool1);
            return true;
          case 1:
            break;
        } 
        str1.enforceInterface("android.support.v4.media.session.IMediaSession");
        String str4 = str1.readString();
        if (str1.readInt() != 0) {
          Bundle bundle = (Bundle)Bundle.CREATOR.createFromParcel((Parcel)str1);
        } else {
          keyEvent = null;
        } 
        if (str1.readInt() != 0)
          resultReceiverWrapper = (MediaSessionCompat.ResultReceiverWrapper)MediaSessionCompat.ResultReceiverWrapper.CREATOR.createFromParcel((Parcel)str1); 
        F0(str4, (Bundle)keyEvent, resultReceiverWrapper);
        param1Parcel2.writeNoException();
        return true;
      } 
      param1Parcel2.writeString("android.support.v4.media.session.IMediaSession");
      return true;
    }
    
    public static class a implements b {
      public static b b;
      
      public IBinder a;
      
      public a(IBinder param2IBinder) {
        this.a = param2IBinder;
      }
      
      public IBinder asBinder() {
        return this.a;
      }
      
      public PlaybackStateCompat g() {
        Parcel parcel1 = Parcel.obtain();
        Parcel parcel2 = Parcel.obtain();
        try {
          PlaybackStateCompat playbackStateCompat;
          parcel1.writeInterfaceToken("android.support.v4.media.session.IMediaSession");
          if (!this.a.transact(28, parcel1, parcel2, 0) && b.a.f() != null) {
            playbackStateCompat = b.a.f().g();
            return playbackStateCompat;
          } 
          parcel2.readException();
          if (parcel2.readInt() != 0) {
            playbackStateCompat = (PlaybackStateCompat)PlaybackStateCompat.CREATOR.createFromParcel(parcel2);
          } else {
            playbackStateCompat = null;
          } 
          return playbackStateCompat;
        } finally {
          parcel2.recycle();
          parcel1.recycle();
        } 
      }
      
      public MediaMetadataCompat k() {
        Parcel parcel1 = Parcel.obtain();
        Parcel parcel2 = Parcel.obtain();
        try {
          MediaMetadataCompat mediaMetadataCompat;
          parcel1.writeInterfaceToken("android.support.v4.media.session.IMediaSession");
          if (!this.a.transact(27, parcel1, parcel2, 0) && b.a.f() != null) {
            mediaMetadataCompat = b.a.f().k();
            return mediaMetadataCompat;
          } 
          parcel2.readException();
          if (parcel2.readInt() != 0) {
            mediaMetadataCompat = (MediaMetadataCompat)MediaMetadataCompat.CREATOR.createFromParcel(parcel2);
          } else {
            mediaMetadataCompat = null;
          } 
          return mediaMetadataCompat;
        } finally {
          parcel2.recycle();
          parcel1.recycle();
        } 
      }
      
      public List<MediaSessionCompat.QueueItem> o() {
        Parcel parcel1 = Parcel.obtain();
        Parcel parcel2 = Parcel.obtain();
        try {
          parcel1.writeInterfaceToken("android.support.v4.media.session.IMediaSession");
          if (!this.a.transact(29, parcel1, parcel2, 0) && b.a.f() != null)
            return b.a.f().o(); 
          parcel2.readException();
          return parcel2.createTypedArrayList(MediaSessionCompat.QueueItem.CREATOR);
        } finally {
          parcel2.recycle();
          parcel1.recycle();
        } 
      }
      
      public void t(a param2a) {
        Parcel parcel1 = Parcel.obtain();
        Parcel parcel2 = Parcel.obtain();
        try {
          IBinder iBinder;
          parcel1.writeInterfaceToken("android.support.v4.media.session.IMediaSession");
          if (param2a != null) {
            iBinder = param2a.asBinder();
          } else {
            iBinder = null;
          } 
          parcel1.writeStrongBinder(iBinder);
          if (!this.a.transact(3, parcel1, parcel2, 0) && b.a.f() != null) {
            b.a.f().t(param2a);
            return;
          } 
          parcel2.readException();
          return;
        } finally {
          parcel2.recycle();
          parcel1.recycle();
        } 
      }
    }
  }
  
  public static class a implements b {
    public static b b;
    
    public IBinder a;
    
    public a(IBinder param1IBinder) {
      this.a = param1IBinder;
    }
    
    public IBinder asBinder() {
      return this.a;
    }
    
    public PlaybackStateCompat g() {
      Parcel parcel1 = Parcel.obtain();
      Parcel parcel2 = Parcel.obtain();
      try {
        PlaybackStateCompat playbackStateCompat;
        parcel1.writeInterfaceToken("android.support.v4.media.session.IMediaSession");
        if (!this.a.transact(28, parcel1, parcel2, 0) && b.a.f() != null) {
          playbackStateCompat = b.a.f().g();
          return playbackStateCompat;
        } 
        parcel2.readException();
        if (parcel2.readInt() != 0) {
          playbackStateCompat = (PlaybackStateCompat)PlaybackStateCompat.CREATOR.createFromParcel(parcel2);
        } else {
          playbackStateCompat = null;
        } 
        return playbackStateCompat;
      } finally {
        parcel2.recycle();
        parcel1.recycle();
      } 
    }
    
    public MediaMetadataCompat k() {
      Parcel parcel1 = Parcel.obtain();
      Parcel parcel2 = Parcel.obtain();
      try {
        MediaMetadataCompat mediaMetadataCompat;
        parcel1.writeInterfaceToken("android.support.v4.media.session.IMediaSession");
        if (!this.a.transact(27, parcel1, parcel2, 0) && b.a.f() != null) {
          mediaMetadataCompat = b.a.f().k();
          return mediaMetadataCompat;
        } 
        parcel2.readException();
        if (parcel2.readInt() != 0) {
          mediaMetadataCompat = (MediaMetadataCompat)MediaMetadataCompat.CREATOR.createFromParcel(parcel2);
        } else {
          mediaMetadataCompat = null;
        } 
        return mediaMetadataCompat;
      } finally {
        parcel2.recycle();
        parcel1.recycle();
      } 
    }
    
    public List<MediaSessionCompat.QueueItem> o() {
      Parcel parcel1 = Parcel.obtain();
      Parcel parcel2 = Parcel.obtain();
      try {
        parcel1.writeInterfaceToken("android.support.v4.media.session.IMediaSession");
        if (!this.a.transact(29, parcel1, parcel2, 0) && b.a.f() != null)
          return b.a.f().o(); 
        parcel2.readException();
        return parcel2.createTypedArrayList(MediaSessionCompat.QueueItem.CREATOR);
      } finally {
        parcel2.recycle();
        parcel1.recycle();
      } 
    }
    
    public void t(a param1a) {
      Parcel parcel1 = Parcel.obtain();
      Parcel parcel2 = Parcel.obtain();
      try {
        IBinder iBinder;
        parcel1.writeInterfaceToken("android.support.v4.media.session.IMediaSession");
        if (param1a != null) {
          iBinder = param1a.asBinder();
        } else {
          iBinder = null;
        } 
        parcel1.writeStrongBinder(iBinder);
        if (!this.a.transact(3, parcel1, parcel2, 0) && b.a.f() != null) {
          b.a.f().t(param1a);
          return;
        } 
        parcel2.readException();
        return;
      } finally {
        parcel2.recycle();
        parcel1.recycle();
      } 
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\android\support\v4\media\session\b.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */