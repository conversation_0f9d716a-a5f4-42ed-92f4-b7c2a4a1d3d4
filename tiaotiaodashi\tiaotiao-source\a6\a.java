package a6;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import androidx.work.impl.WorkDatabase;
import g6.g;
import g6.h;
import h6.e;
import x5.j;
import y5.i;

public class a {
  public static final String a = j.f("Alarms");
  
  public static void a(Context paramContext, i parami, String paramString) {
    h h = parami.p().A();
    g g = h.b(paramString);
    if (g != null) {
      b(paramContext, paramString, g.b);
      j.c().a(a, String.format("Removing SystemIdInfo for workSpecId (%s)", new Object[] { paramString }), new Throwable[0]);
      h.d(paramString);
    } 
  }
  
  public static void b(Context paramContext, String paramString, int paramInt) {
    AlarmManager alarmManager = (AlarmManager)paramContext.getSystemService("alarm");
    PendingIntent pendingIntent = PendingIntent.getService(paramContext, paramInt, androidx.work.impl.background.systemalarm.a.b(paramContext, paramString), 603979776);
    if (pendingIntent != null && alarmManager != null) {
      j.c().a(a, String.format("Cancelling existing alarm with (workSpecId, systemId) (%s, %s)", new Object[] { paramString, Integer.valueOf(paramInt) }), new Throwable[0]);
      alarmManager.cancel(pendingIntent);
    } 
  }
  
  public static void c(Context paramContext, i parami, String paramString, long paramLong) {
    int j;
    WorkDatabase workDatabase = parami.p();
    h h = workDatabase.A();
    g g = h.b(paramString);
    if (g != null) {
      b(paramContext, paramString, g.b);
      j = g.b;
    } else {
      j = (new e(workDatabase)).b();
      h.c(new g(paramString, j));
    } 
    d(paramContext, paramString, j, paramLong);
  }
  
  public static void d(Context paramContext, String paramString, int paramInt, long paramLong) {
    AlarmManager alarmManager = (AlarmManager)paramContext.getSystemService("alarm");
    PendingIntent pendingIntent = PendingIntent.getService(paramContext, paramInt, androidx.work.impl.background.systemalarm.a.b(paramContext, paramString), 201326592);
    if (alarmManager != null)
      alarmManager.setExact(0, paramLong, pendingIntent); 
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a6\a.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */