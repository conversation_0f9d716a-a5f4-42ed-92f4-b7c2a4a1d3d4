package a2;

import android.media.MediaDataSource;
import android.media.MediaMetadataRetriever;
import android.system.Os;
import android.util.Log;
import java.io.Closeable;
import java.io.FileDescriptor;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class b {
  public static void a(FileDescriptor paramFileDescriptor) {
    try {
      a.a(paramFileDescriptor);
    } catch (Exception exception) {
      Log.e("ExifInterfaceUtils", "Error closing fd.");
    } 
  }
  
  public static void b(Closeable paramCloseable) {
    if (paramCloseable != null)
      try {
        paramCloseable.close();
      } catch (RuntimeException runtimeException) {
        throw runtimeException;
      } catch (Exception exception) {} 
  }
  
  public static long[] c(Object paramObject) {
    if (paramObject instanceof int[]) {
      int[] arrayOfInt = (int[])paramObject;
      paramObject = new long[arrayOfInt.length];
      for (byte b1 = 0; b1 < arrayOfInt.length; b1++)
        paramObject[b1] = arrayOfInt[b1]; 
      return (long[])paramObject;
    } 
    return (paramObject instanceof long[]) ? (long[])paramObject : null;
  }
  
  public static int d(InputStream paramInputStream, OutputStream paramOutputStream) {
    byte[] arrayOfByte = new byte[8192];
    int i = 0;
    while (true) {
      int j = paramInputStream.read(arrayOfByte);
      if (j != -1) {
        i += j;
        paramOutputStream.write(arrayOfByte, 0, j);
        continue;
      } 
      return i;
    } 
  }
  
  public static void e(InputStream paramInputStream, OutputStream paramOutputStream, int paramInt) {
    byte[] arrayOfByte = new byte[8192];
    while (paramInt > 0) {
      int j = Math.min(paramInt, 8192);
      int i = paramInputStream.read(arrayOfByte, 0, j);
      if (i == j) {
        paramInt -= i;
        paramOutputStream.write(arrayOfByte, 0, i);
        continue;
      } 
      throw new IOException("Failed to copy the given amount of bytes from the inputstream to the output stream.");
    } 
  }
  
  public static boolean f(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2) {
    if (paramArrayOfbyte1 == null || paramArrayOfbyte2 == null)
      return false; 
    if (paramArrayOfbyte1.length < paramArrayOfbyte2.length)
      return false; 
    for (byte b1 = 0; b1 < paramArrayOfbyte2.length; b1++) {
      if (paramArrayOfbyte1[b1] != paramArrayOfbyte2[b1])
        return false; 
    } 
    return true;
  }
  
  public static class a {
    public static void a(FileDescriptor param1FileDescriptor) {
      Os.close(param1FileDescriptor);
    }
    
    public static FileDescriptor b(FileDescriptor param1FileDescriptor) {
      return Os.dup(param1FileDescriptor);
    }
    
    public static long c(FileDescriptor param1FileDescriptor, long param1Long, int param1Int) {
      return Os.lseek(param1FileDescriptor, param1Long, param1Int);
    }
  }
  
  public static class b {
    public static void a(MediaMetadataRetriever param1MediaMetadataRetriever, MediaDataSource param1MediaDataSource) {
      param1MediaMetadataRetriever.setDataSource(param1MediaDataSource);
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a2\b.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */