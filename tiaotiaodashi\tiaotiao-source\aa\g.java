package aa;

import b9.c;
import b9.i;
import b9.y;
import java.util.List;
import u8.q1;
import v8.p1;

public interface g {
  boolean a(i parami);
  
  q1[] c();
  
  c d();
  
  void e(b paramb, long paramLong1, long paramLong2);
  
  void release();
  
  public static interface a {
    g a(int param1Int, q1 param1q1, boolean param1Boolean, List<q1> param1List, y param1y, p1 param1p1);
  }
  
  public static interface b {
    y b(int param1Int1, int param1Int2);
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\aa\g.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */