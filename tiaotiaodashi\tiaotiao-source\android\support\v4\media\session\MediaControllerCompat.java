package android.support.v4.media.session;

import android.content.Context;
import android.media.MediaMetadata;
import android.media.session.MediaController;
import android.media.session.MediaSession;
import android.media.session.PlaybackState;
import android.os.Build;
import android.os.Bundle;
import android.os.IBinder;
import android.os.RemoteException;
import android.os.ResultReceiver;
import android.support.v4.media.MediaMetadataCompat;
import android.util.Log;
import androidx.media.AudioAttributesCompat;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import w0.h;

public final class MediaControllerCompat {
  public final b a;
  
  public final MediaSessionCompat.Token b;
  
  public final ConcurrentHashMap<a, Boolean> c = new ConcurrentHashMap<a, Boolean>();
  
  public MediaControllerCompat(Context paramContext, MediaSessionCompat paramMediaSessionCompat) {
    if (paramMediaSessionCompat != null) {
      MediaControllerImplApi21 mediaControllerImplApi21;
      MediaSessionCompat.Token token = paramMediaSessionCompat.c();
      this.b = token;
      if (Build.VERSION.SDK_INT >= 29) {
        mediaControllerImplApi21 = new c(paramContext, token);
      } else {
        mediaControllerImplApi21 = new MediaControllerImplApi21((Context)mediaControllerImplApi21, token);
      } 
      this.a = mediaControllerImplApi21;
      return;
    } 
    throw new IllegalArgumentException("session must not be null");
  }
  
  public MediaMetadataCompat a() {
    return this.a.k();
  }
  
  public PlaybackStateCompat b() {
    return this.a.g();
  }
  
  public List<MediaSessionCompat.QueueItem> c() {
    return this.a.o();
  }
  
  public static class MediaControllerImplApi21 implements b {
    public final MediaController a;
    
    public final Object b = new Object();
    
    public final List<MediaControllerCompat.a> c = new ArrayList<MediaControllerCompat.a>();
    
    public HashMap<MediaControllerCompat.a, a> d = new HashMap<MediaControllerCompat.a, a>();
    
    public final MediaSessionCompat.Token e;
    
    public MediaControllerImplApi21(Context param1Context, MediaSessionCompat.Token param1Token) {
      this.e = param1Token;
      this.a = new MediaController(param1Context, (MediaSession.Token)param1Token.g());
      if (param1Token.c() == null)
        b(); 
    }
    
    public void a() {
      if (this.e.c() == null)
        return; 
      for (MediaControllerCompat.a a : this.c) {
        a a1 = new a(a);
        this.d.put(a, a1);
        a.b = a1;
        try {
          this.e.c().t(a1);
          a.i(13, null, null);
        } catch (RemoteException remoteException) {
          Log.e("MediaControllerCompat", "Dead object in registerCallback.", (Throwable)remoteException);
          break;
        } 
      } 
      this.c.clear();
    }
    
    public final void b() {
      c("android.support.v4.media.session.command.GET_EXTRA_BINDER", null, new ExtraBinderRequestResultReceiver(this));
    }
    
    public void c(String param1String, Bundle param1Bundle, ResultReceiver param1ResultReceiver) {
      this.a.sendCommand(param1String, param1Bundle, param1ResultReceiver);
    }
    
    public PlaybackStateCompat g() {
      if (this.e.c() != null)
        try {
          return this.e.c().g();
        } catch (RemoteException remoteException) {
          Log.e("MediaControllerCompat", "Dead object in getPlaybackState.", (Throwable)remoteException);
        }  
      PlaybackState playbackState = this.a.getPlaybackState();
      if (playbackState != null) {
        PlaybackStateCompat playbackStateCompat = PlaybackStateCompat.c(playbackState);
      } else {
        playbackState = null;
      } 
      return (PlaybackStateCompat)playbackState;
    }
    
    public MediaMetadataCompat k() {
      MediaMetadata mediaMetadata = this.a.getMetadata();
      if (mediaMetadata != null) {
        MediaMetadataCompat mediaMetadataCompat = MediaMetadataCompat.d(mediaMetadata);
      } else {
        mediaMetadata = null;
      } 
      return (MediaMetadataCompat)mediaMetadata;
    }
    
    public List<MediaSessionCompat.QueueItem> o() {
      List<?> list = this.a.getQueue();
      if (list != null) {
        list = MediaSessionCompat.QueueItem.d(list);
      } else {
        list = null;
      } 
      return (List)list;
    }
    
    public static class ExtraBinderRequestResultReceiver extends ResultReceiver {
      public WeakReference<MediaControllerCompat.MediaControllerImplApi21> a;
      
      public ExtraBinderRequestResultReceiver(MediaControllerCompat.MediaControllerImplApi21 param2MediaControllerImplApi21) {
        super(null);
        this.a = new WeakReference<MediaControllerCompat.MediaControllerImplApi21>(param2MediaControllerImplApi21);
      }
      
      public void onReceiveResult(int param2Int, Bundle param2Bundle) {
        MediaControllerCompat.MediaControllerImplApi21 mediaControllerImplApi21 = this.a.get();
        if (mediaControllerImplApi21 == null || param2Bundle == null)
          return; 
        synchronized (mediaControllerImplApi21.b) {
          mediaControllerImplApi21.e.h(b.a.c(h.a(param2Bundle, "android.support.v4.media.session.EXTRA_BINDER")));
          mediaControllerImplApi21.e.i(m5.a.b(param2Bundle, "android.support.v4.media.session.SESSION_TOKEN2"));
          mediaControllerImplApi21.a();
          return;
        } 
      }
    }
    
    public static class a extends MediaControllerCompat.a.b {
      public a(MediaControllerCompat.a param2a) {
        super(param2a);
      }
      
      public void O(Bundle param2Bundle) {
        throw new AssertionError();
      }
      
      public void R(List<MediaSessionCompat.QueueItem> param2List) {
        throw new AssertionError();
      }
      
      public void X0(ParcelableVolumeInfo param2ParcelableVolumeInfo) {
        throw new AssertionError();
      }
      
      public void p0(CharSequence param2CharSequence) {
        throw new AssertionError();
      }
      
      public void s0() {
        throw new AssertionError();
      }
      
      public void u0(MediaMetadataCompat param2MediaMetadataCompat) {
        throw new AssertionError();
      }
    }
  }
  
  public static class ExtraBinderRequestResultReceiver extends ResultReceiver {
    public WeakReference<MediaControllerCompat.MediaControllerImplApi21> a;
    
    public ExtraBinderRequestResultReceiver(MediaControllerCompat.MediaControllerImplApi21 param1MediaControllerImplApi21) {
      super(null);
      this.a = new WeakReference<MediaControllerCompat.MediaControllerImplApi21>(param1MediaControllerImplApi21);
    }
    
    public void onReceiveResult(int param1Int, Bundle param1Bundle) {
      MediaControllerCompat.MediaControllerImplApi21 mediaControllerImplApi21 = this.a.get();
      if (mediaControllerImplApi21 == null || param1Bundle == null)
        return; 
      synchronized (mediaControllerImplApi21.b) {
        mediaControllerImplApi21.e.h(b.a.c(h.a(param1Bundle, "android.support.v4.media.session.EXTRA_BINDER")));
        mediaControllerImplApi21.e.i(m5.a.b(param1Bundle, "android.support.v4.media.session.SESSION_TOKEN2"));
        mediaControllerImplApi21.a();
        return;
      } 
    }
  }
  
  public static class a extends a.b {
    public a(MediaControllerCompat.a param1a) {
      super(param1a);
    }
    
    public void O(Bundle param1Bundle) {
      throw new AssertionError();
    }
    
    public void R(List<MediaSessionCompat.QueueItem> param1List) {
      throw new AssertionError();
    }
    
    public void X0(ParcelableVolumeInfo param1ParcelableVolumeInfo) {
      throw new AssertionError();
    }
    
    public void p0(CharSequence param1CharSequence) {
      throw new AssertionError();
    }
    
    public void s0() {
      throw new AssertionError();
    }
    
    public void u0(MediaMetadataCompat param1MediaMetadataCompat) {
      throw new AssertionError();
    }
  }
  
  public static abstract class a implements IBinder.DeathRecipient {
    public final MediaController.Callback a = new a(this);
    
    public a b;
    
    public void a(MediaControllerCompat.d param1d) {}
    
    public void b(Bundle param1Bundle) {}
    
    public void binderDied() {
      i(8, null, null);
    }
    
    public void c(MediaMetadataCompat param1MediaMetadataCompat) {}
    
    public void d(PlaybackStateCompat param1PlaybackStateCompat) {}
    
    public void e(List<MediaSessionCompat.QueueItem> param1List) {}
    
    public void f(CharSequence param1CharSequence) {}
    
    public void g() {}
    
    public void h(String param1String, Bundle param1Bundle) {}
    
    public void i(int param1Int, Object param1Object, Bundle param1Bundle) {}
    
    public static class a extends MediaController.Callback {
      public final WeakReference<MediaControllerCompat.a> a;
      
      public a(MediaControllerCompat.a param2a) {
        this.a = new WeakReference<MediaControllerCompat.a>(param2a);
      }
      
      public void onAudioInfoChanged(MediaController.PlaybackInfo param2PlaybackInfo) {
        MediaControllerCompat.a a1 = this.a.get();
        if (a1 != null)
          a1.a(new MediaControllerCompat.d(param2PlaybackInfo.getPlaybackType(), AudioAttributesCompat.c(param2PlaybackInfo.getAudioAttributes()), param2PlaybackInfo.getVolumeControl(), param2PlaybackInfo.getMaxVolume(), param2PlaybackInfo.getCurrentVolume())); 
      }
      
      public void onExtrasChanged(Bundle param2Bundle) {
        MediaSessionCompat.a(param2Bundle);
        MediaControllerCompat.a a1 = this.a.get();
        if (a1 != null)
          a1.b(param2Bundle); 
      }
      
      public void onMetadataChanged(MediaMetadata param2MediaMetadata) {
        MediaControllerCompat.a a1 = this.a.get();
        if (a1 != null)
          a1.c(MediaMetadataCompat.d(param2MediaMetadata)); 
      }
      
      public void onPlaybackStateChanged(PlaybackState param2PlaybackState) {
        MediaControllerCompat.a a1 = this.a.get();
        if (a1 != null && a1.b == null)
          a1.d(PlaybackStateCompat.c(param2PlaybackState)); 
      }
      
      public void onQueueChanged(List<MediaSession.QueueItem> param2List) {
        MediaControllerCompat.a a1 = this.a.get();
        if (a1 != null)
          a1.e(MediaSessionCompat.QueueItem.d(param2List)); 
      }
      
      public void onQueueTitleChanged(CharSequence param2CharSequence) {
        MediaControllerCompat.a a1 = this.a.get();
        if (a1 != null)
          a1.f(param2CharSequence); 
      }
      
      public void onSessionDestroyed() {
        MediaControllerCompat.a a1 = this.a.get();
        if (a1 != null)
          a1.g(); 
      }
      
      public void onSessionEvent(String param2String, Bundle param2Bundle) {
        MediaSessionCompat.a(param2Bundle);
        MediaControllerCompat.a a1 = this.a.get();
        if (a1 != null) {
          a a2 = a1.b;
          a1.h(param2String, param2Bundle);
        } 
      }
    }
    
    public static class b extends a.a {
      public final WeakReference<MediaControllerCompat.a> a;
      
      public b(MediaControllerCompat.a param2a) {
        this.a = new WeakReference<MediaControllerCompat.a>(param2a);
      }
      
      public void D0(int param2Int) {
        MediaControllerCompat.a a1 = this.a.get();
        if (a1 != null)
          a1.i(12, Integer.valueOf(param2Int), null); 
      }
      
      public void N() {
        MediaControllerCompat.a a1 = this.a.get();
        if (a1 != null)
          a1.i(13, null, null); 
      }
      
      public void U0(PlaybackStateCompat param2PlaybackStateCompat) {
        MediaControllerCompat.a a1 = this.a.get();
        if (a1 != null)
          a1.i(2, param2PlaybackStateCompat, null); 
      }
      
      public void V0(String param2String, Bundle param2Bundle) {
        MediaControllerCompat.a a1 = this.a.get();
        if (a1 != null)
          a1.i(1, param2String, param2Bundle); 
      }
      
      public void j0(boolean param2Boolean) {
        MediaControllerCompat.a a1 = this.a.get();
        if (a1 != null)
          a1.i(11, Boolean.valueOf(param2Boolean), null); 
      }
      
      public void o0(boolean param2Boolean) {}
      
      public void q(int param2Int) {
        MediaControllerCompat.a a1 = this.a.get();
        if (a1 != null)
          a1.i(9, Integer.valueOf(param2Int), null); 
      }
    }
  }
  
  public static class a extends MediaController.Callback {
    public final WeakReference<MediaControllerCompat.a> a;
    
    public a(MediaControllerCompat.a param1a) {
      this.a = new WeakReference<MediaControllerCompat.a>(param1a);
    }
    
    public void onAudioInfoChanged(MediaController.PlaybackInfo param1PlaybackInfo) {
      MediaControllerCompat.a a1 = this.a.get();
      if (a1 != null)
        a1.a(new MediaControllerCompat.d(param1PlaybackInfo.getPlaybackType(), AudioAttributesCompat.c(param1PlaybackInfo.getAudioAttributes()), param1PlaybackInfo.getVolumeControl(), param1PlaybackInfo.getMaxVolume(), param1PlaybackInfo.getCurrentVolume())); 
    }
    
    public void onExtrasChanged(Bundle param1Bundle) {
      MediaSessionCompat.a(param1Bundle);
      MediaControllerCompat.a a1 = this.a.get();
      if (a1 != null)
        a1.b(param1Bundle); 
    }
    
    public void onMetadataChanged(MediaMetadata param1MediaMetadata) {
      MediaControllerCompat.a a1 = this.a.get();
      if (a1 != null)
        a1.c(MediaMetadataCompat.d(param1MediaMetadata)); 
    }
    
    public void onPlaybackStateChanged(PlaybackState param1PlaybackState) {
      MediaControllerCompat.a a1 = this.a.get();
      if (a1 != null && a1.b == null)
        a1.d(PlaybackStateCompat.c(param1PlaybackState)); 
    }
    
    public void onQueueChanged(List<MediaSession.QueueItem> param1List) {
      MediaControllerCompat.a a1 = this.a.get();
      if (a1 != null)
        a1.e(MediaSessionCompat.QueueItem.d(param1List)); 
    }
    
    public void onQueueTitleChanged(CharSequence param1CharSequence) {
      MediaControllerCompat.a a1 = this.a.get();
      if (a1 != null)
        a1.f(param1CharSequence); 
    }
    
    public void onSessionDestroyed() {
      MediaControllerCompat.a a1 = this.a.get();
      if (a1 != null)
        a1.g(); 
    }
    
    public void onSessionEvent(String param1String, Bundle param1Bundle) {
      MediaSessionCompat.a(param1Bundle);
      MediaControllerCompat.a a1 = this.a.get();
      if (a1 != null) {
        a a2 = a1.b;
        a1.h(param1String, param1Bundle);
      } 
    }
  }
  
  public static class b extends a.a {
    public final WeakReference<MediaControllerCompat.a> a;
    
    public b(MediaControllerCompat.a param1a) {
      this.a = new WeakReference<MediaControllerCompat.a>(param1a);
    }
    
    public void D0(int param1Int) {
      MediaControllerCompat.a a1 = this.a.get();
      if (a1 != null)
        a1.i(12, Integer.valueOf(param1Int), null); 
    }
    
    public void N() {
      MediaControllerCompat.a a1 = this.a.get();
      if (a1 != null)
        a1.i(13, null, null); 
    }
    
    public void U0(PlaybackStateCompat param1PlaybackStateCompat) {
      MediaControllerCompat.a a1 = this.a.get();
      if (a1 != null)
        a1.i(2, param1PlaybackStateCompat, null); 
    }
    
    public void V0(String param1String, Bundle param1Bundle) {
      MediaControllerCompat.a a1 = this.a.get();
      if (a1 != null)
        a1.i(1, param1String, param1Bundle); 
    }
    
    public void j0(boolean param1Boolean) {
      MediaControllerCompat.a a1 = this.a.get();
      if (a1 != null)
        a1.i(11, Boolean.valueOf(param1Boolean), null); 
    }
    
    public void o0(boolean param1Boolean) {}
    
    public void q(int param1Int) {
      MediaControllerCompat.a a1 = this.a.get();
      if (a1 != null)
        a1.i(9, Integer.valueOf(param1Int), null); 
    }
  }
  
  public static interface b {
    PlaybackStateCompat g();
    
    MediaMetadataCompat k();
    
    List<MediaSessionCompat.QueueItem> o();
  }
  
  public static class c extends MediaControllerImplApi21 {
    public c(Context param1Context, MediaSessionCompat.Token param1Token) {
      super(param1Context, param1Token);
    }
  }
  
  public static final class d {
    public final int a;
    
    public final AudioAttributesCompat b;
    
    public final int c;
    
    public final int d;
    
    public final int e;
    
    public d(int param1Int1, AudioAttributesCompat param1AudioAttributesCompat, int param1Int2, int param1Int3, int param1Int4) {
      this.a = param1Int1;
      this.b = param1AudioAttributesCompat;
      this.c = param1Int2;
      this.d = param1Int3;
      this.e = param1Int4;
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\android\support\v4\media\session\MediaControllerCompat.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */