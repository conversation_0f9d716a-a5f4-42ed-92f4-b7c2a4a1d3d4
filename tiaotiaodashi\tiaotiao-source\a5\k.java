package a5;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.BitSet;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;

public class k {
  public static final byte[] a = new byte[] { 112, 114, 111, 0 };
  
  public static final byte[] b = new byte[] { 112, 114, 109, 0 };
  
  public static void A(InputStream paramInputStream) {
    c.h(paramInputStream);
    int j = c.j(paramInputStream);
    if (j == 6)
      return; 
    int i = j;
    if (j == 7)
      return; 
    while (i > 0) {
      c.j(paramInputStream);
      for (j = c.j(paramInputStream); j > 0; j--)
        c.h(paramInputStream); 
      i--;
    } 
  }
  
  public static boolean B(OutputStream paramOutputStream, byte[] paramArrayOfbyte, b[] paramArrayOfb) {
    if (Arrays.equals(paramArrayOfbyte, l.a)) {
      N(paramOutputStream, paramArrayOfb);
      return true;
    } 
    if (Arrays.equals(paramArrayOfbyte, l.b)) {
      M(paramOutputStream, paramArrayOfb);
      return true;
    } 
    if (Arrays.equals(paramArrayOfbyte, l.d)) {
      K(paramOutputStream, paramArrayOfb);
      return true;
    } 
    if (Arrays.equals(paramArrayOfbyte, l.c)) {
      L(paramOutputStream, paramArrayOfb);
      return true;
    } 
    if (Arrays.equals(paramArrayOfbyte, l.e)) {
      J(paramOutputStream, paramArrayOfb);
      return true;
    } 
    return false;
  }
  
  public static void C(OutputStream paramOutputStream, b paramb) {
    int[] arrayOfInt = paramb.h;
    int j = arrayOfInt.length;
    byte b1 = 0;
    int i = 0;
    while (b1 < j) {
      Integer integer = Integer.valueOf(arrayOfInt[b1]);
      c.p(paramOutputStream, integer.intValue() - i);
      i = integer.intValue();
      b1++;
    } 
  }
  
  public static m D(b[] paramArrayOfb) {
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    try {
      c.p(byteArrayOutputStream, paramArrayOfb.length);
      byte b1 = 0;
      int i = 2;
      while (b1 < paramArrayOfb.length) {
        b b2 = paramArrayOfb[b1];
        c.q(byteArrayOutputStream, b2.c);
        c.q(byteArrayOutputStream, b2.d);
        c.q(byteArrayOutputStream, b2.g);
        String str = j(b2.a, b2.b, l.a);
        int j = c.k(str);
        c.p(byteArrayOutputStream, j);
        i = i + 4 + 4 + 4 + 2 + j * 1;
        c.n(byteArrayOutputStream, str);
        b1++;
      } 
      byte[] arrayOfByte = byteArrayOutputStream.toByteArray();
      if (i == arrayOfByte.length)
        return new m(d.DEX_FILES, i, arrayOfByte, false); 
      StringBuilder stringBuilder = new StringBuilder();
      this();
      stringBuilder.append("Expected size ");
      stringBuilder.append(i);
      stringBuilder.append(", does not match actual size ");
      stringBuilder.append(arrayOfByte.length);
      throw c.c(stringBuilder.toString());
    } finally {
      try {
        byteArrayOutputStream.close();
      } finally {
        byteArrayOutputStream = null;
      } 
    } 
  }
  
  public static void E(OutputStream paramOutputStream, byte[] paramArrayOfbyte) {
    paramOutputStream.write(a);
    paramOutputStream.write(paramArrayOfbyte);
  }
  
  public static void F(OutputStream paramOutputStream, b paramb) {
    I(paramOutputStream, paramb);
    C(paramOutputStream, paramb);
    H(paramOutputStream, paramb);
  }
  
  public static void G(OutputStream paramOutputStream, b paramb, String paramString) {
    c.p(paramOutputStream, c.k(paramString));
    c.p(paramOutputStream, paramb.e);
    c.q(paramOutputStream, paramb.f);
    c.q(paramOutputStream, paramb.c);
    c.q(paramOutputStream, paramb.g);
    c.n(paramOutputStream, paramString);
  }
  
  public static void H(OutputStream paramOutputStream, b paramb) {
    byte[] arrayOfByte = new byte[k(paramb.g)];
    for (Map.Entry<Integer, Integer> entry : paramb.i.entrySet()) {
      int j = ((Integer)entry.getKey()).intValue();
      int i = ((Integer)entry.getValue()).intValue();
      if ((i & 0x2) != 0)
        z(arrayOfByte, 2, j, paramb); 
      if ((i & 0x4) != 0)
        z(arrayOfByte, 4, j, paramb); 
    } 
    paramOutputStream.write(arrayOfByte);
  }
  
  public static void I(OutputStream paramOutputStream, b paramb) {
    Iterator<Map.Entry> iterator = paramb.i.entrySet().iterator();
    for (int i = 0; iterator.hasNext(); i = j) {
      Map.Entry entry = iterator.next();
      int j = ((Integer)entry.getKey()).intValue();
      if ((((Integer)entry.getValue()).intValue() & 0x1) == 0)
        continue; 
      c.p(paramOutputStream, j - i);
      c.p(paramOutputStream, 0);
    } 
  }
  
  public static void J(OutputStream paramOutputStream, b[] paramArrayOfb) {
    c.p(paramOutputStream, paramArrayOfb.length);
    int i = paramArrayOfb.length;
    for (byte b1 = 0; b1 < i; b1++) {
      b b3 = paramArrayOfb[b1];
      String str = j(b3.a, b3.b, l.e);
      c.p(paramOutputStream, c.k(str));
      c.p(paramOutputStream, b3.i.size());
      c.p(paramOutputStream, b3.h.length);
      c.q(paramOutputStream, b3.c);
      c.n(paramOutputStream, str);
      Iterator<Integer> iterator = b3.i.keySet().iterator();
      while (iterator.hasNext())
        c.p(paramOutputStream, ((Integer)iterator.next()).intValue()); 
      int[] arrayOfInt = b3.h;
      int j = arrayOfInt.length;
      for (byte b2 = 0; b2 < j; b2++)
        c.p(paramOutputStream, arrayOfInt[b2]); 
    } 
  }
  
  public static void K(OutputStream paramOutputStream, b[] paramArrayOfb) {
    c.r(paramOutputStream, paramArrayOfb.length);
    int i = paramArrayOfb.length;
    for (byte b1 = 0; b1 < i; b1++) {
      b b2 = paramArrayOfb[b1];
      int j = b2.i.size();
      String str = j(b2.a, b2.b, l.d);
      c.p(paramOutputStream, c.k(str));
      c.p(paramOutputStream, b2.h.length);
      c.q(paramOutputStream, (j * 4));
      c.q(paramOutputStream, b2.c);
      c.n(paramOutputStream, str);
      Iterator<Integer> iterator = b2.i.keySet().iterator();
      while (iterator.hasNext()) {
        c.p(paramOutputStream, ((Integer)iterator.next()).intValue());
        c.p(paramOutputStream, 0);
      } 
      int[] arrayOfInt = b2.h;
      int m = arrayOfInt.length;
      for (j = 0; j < m; j++)
        c.p(paramOutputStream, arrayOfInt[j]); 
    } 
  }
  
  public static void L(OutputStream paramOutputStream, b[] paramArrayOfb) {
    byte[] arrayOfByte = b(paramArrayOfb, l.c);
    c.r(paramOutputStream, paramArrayOfb.length);
    c.m(paramOutputStream, arrayOfByte);
  }
  
  public static void M(OutputStream paramOutputStream, b[] paramArrayOfb) {
    byte[] arrayOfByte = b(paramArrayOfb, l.b);
    c.r(paramOutputStream, paramArrayOfb.length);
    c.m(paramOutputStream, arrayOfByte);
  }
  
  public static void N(OutputStream paramOutputStream, b[] paramArrayOfb) {
    O(paramOutputStream, paramArrayOfb);
  }
  
  public static void O(OutputStream paramOutputStream, b[] paramArrayOfb) {
    int i;
    ArrayList<m> arrayList = new ArrayList(3);
    ArrayList<byte[]> arrayList1 = new ArrayList(3);
    arrayList.add(D(paramArrayOfb));
    arrayList.add(c(paramArrayOfb));
    arrayList.add(d(paramArrayOfb));
    long l = l.a.length + a.length + 4L + (arrayList.size() * 16);
    c.q(paramOutputStream, arrayList.size());
    byte b2 = 0;
    byte b1 = 0;
    while (true) {
      i = b2;
      if (b1 < arrayList.size()) {
        byte[] arrayOfByte;
        m m = arrayList.get(b1);
        c.q(paramOutputStream, m.a.b());
        c.q(paramOutputStream, l);
        if (m.d) {
          arrayOfByte = m.c;
          long l1 = arrayOfByte.length;
          arrayOfByte = c.b(arrayOfByte);
          arrayList1.add(arrayOfByte);
          c.q(paramOutputStream, arrayOfByte.length);
          c.q(paramOutputStream, l1);
          i = arrayOfByte.length;
        } else {
          arrayList1.add(((m)arrayOfByte).c);
          c.q(paramOutputStream, ((m)arrayOfByte).c.length);
          c.q(paramOutputStream, 0L);
          i = ((m)arrayOfByte).c.length;
        } 
        l += i;
        b1++;
        continue;
      } 
      break;
    } 
    while (i < arrayList1.size()) {
      paramOutputStream.write(arrayList1.get(i));
      i++;
    } 
  }
  
  public static int a(b paramb) {
    Iterator<Map.Entry> iterator = paramb.i.entrySet().iterator();
    int i;
    for (i = 0; iterator.hasNext(); i |= ((Integer)((Map.Entry)iterator.next()).getValue()).intValue());
    return i;
  }
  
  public static byte[] b(b[] paramArrayOfb, byte[] paramArrayOfbyte) {
    int i1 = paramArrayOfb.length;
    int m = 0;
    int n = 0;
    int j = 0;
    int i = 0;
    while (j < i1) {
      b b1 = paramArrayOfb[j];
      i += c.k(j(b1.a, b1.b, paramArrayOfbyte)) + 16 + b1.e * 2 + b1.f + k(b1.g);
      j++;
    } 
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(i);
    if (Arrays.equals(paramArrayOfbyte, l.c)) {
      m = paramArrayOfb.length;
      for (j = n; j < m; j++) {
        b b1 = paramArrayOfb[j];
        G(byteArrayOutputStream, b1, j(b1.a, b1.b, paramArrayOfbyte));
        F(byteArrayOutputStream, b1);
      } 
    } else {
      n = paramArrayOfb.length;
      for (j = 0; j < n; j++) {
        b b1 = paramArrayOfb[j];
        G(byteArrayOutputStream, b1, j(b1.a, b1.b, paramArrayOfbyte));
      } 
      n = paramArrayOfb.length;
      for (j = m; j < n; j++)
        F(byteArrayOutputStream, paramArrayOfb[j]); 
    } 
    if (byteArrayOutputStream.size() == i)
      return byteArrayOutputStream.toByteArray(); 
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("The bytes saved do not match expectation. actual=");
    stringBuilder.append(byteArrayOutputStream.size());
    stringBuilder.append(" expected=");
    stringBuilder.append(i);
    throw c.c(stringBuilder.toString());
  }
  
  public static m c(b[] paramArrayOfb) {
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    byte b1 = 0;
    int i = 0;
    try {
      m m;
      while (b1 < paramArrayOfb.length) {
        b b2 = paramArrayOfb[b1];
        c.p(byteArrayOutputStream, b1);
        c.p(byteArrayOutputStream, b2.e);
        i = i + 2 + 2 + b2.e * 2;
        C(byteArrayOutputStream, b2);
        b1++;
      } 
      byte[] arrayOfByte = byteArrayOutputStream.toByteArray();
      if (i == arrayOfByte.length) {
        m = new m(d.CLASSES, i, arrayOfByte, true);
        return m;
      } 
      StringBuilder stringBuilder = new StringBuilder();
      this();
      stringBuilder.append("Expected size ");
      stringBuilder.append(i);
      stringBuilder.append(", does not match actual size ");
      stringBuilder.append(m.length);
      throw c.c(stringBuilder.toString());
    } finally {
      try {
        byteArrayOutputStream.close();
      } finally {
        byteArrayOutputStream = null;
      } 
    } 
  }
  
  public static m d(b[] paramArrayOfb) {
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    byte b1 = 0;
    int i = 0;
    try {
      m m;
      while (b1 < paramArrayOfb.length) {
        b b2 = paramArrayOfb[b1];
        int j = a(b2);
        byte[] arrayOfByte1 = e(b2);
        byte[] arrayOfByte2 = f(b2);
        c.p(byteArrayOutputStream, b1);
        int n = arrayOfByte1.length + 2 + arrayOfByte2.length;
        c.q(byteArrayOutputStream, n);
        c.p(byteArrayOutputStream, j);
        byteArrayOutputStream.write(arrayOfByte1);
        byteArrayOutputStream.write(arrayOfByte2);
        i = i + 2 + 4 + n;
        b1++;
      } 
      byte[] arrayOfByte = byteArrayOutputStream.toByteArray();
      if (i == arrayOfByte.length) {
        m = new m(d.METHODS, i, arrayOfByte, true);
        return m;
      } 
      StringBuilder stringBuilder = new StringBuilder();
      this();
      stringBuilder.append("Expected size ");
      stringBuilder.append(i);
      stringBuilder.append(", does not match actual size ");
      stringBuilder.append(m.length);
      throw c.c(stringBuilder.toString());
    } finally {
      try {
        byteArrayOutputStream.close();
      } finally {
        byteArrayOutputStream = null;
      } 
    } 
  }
  
  public static byte[] e(b paramb) {
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    try {
      H(byteArrayOutputStream, paramb);
      return byteArrayOutputStream.toByteArray();
    } finally {
      try {
        byteArrayOutputStream.close();
      } finally {
        byteArrayOutputStream = null;
      } 
    } 
  }
  
  public static byte[] f(b paramb) {
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    try {
      I(byteArrayOutputStream, paramb);
      return byteArrayOutputStream.toByteArray();
    } finally {
      try {
        byteArrayOutputStream.close();
      } finally {
        byteArrayOutputStream = null;
      } 
    } 
  }
  
  public static String g(String paramString1, String paramString2) {
    if ("!".equals(paramString2))
      return paramString1.replace(":", "!"); 
    String str = paramString1;
    if (":".equals(paramString2))
      str = paramString1.replace("!", ":"); 
    return str;
  }
  
  public static String h(String paramString) {
    int j = paramString.indexOf("!");
    int i = j;
    if (j < 0)
      i = paramString.indexOf(":"); 
    String str = paramString;
    if (i > 0)
      str = paramString.substring(i + 1); 
    return str;
  }
  
  public static b i(b[] paramArrayOfb, String paramString) {
    if (paramArrayOfb.length <= 0)
      return null; 
    paramString = h(paramString);
    for (byte b1 = 0; b1 < paramArrayOfb.length; b1++) {
      if ((paramArrayOfb[b1]).b.equals(paramString))
        return paramArrayOfb[b1]; 
    } 
    return null;
  }
  
  public static String j(String paramString1, String paramString2, byte[] paramArrayOfbyte) {
    String str = l.a(paramArrayOfbyte);
    if (paramString1.length() <= 0)
      return g(paramString2, str); 
    if (paramString2.equals("classes.dex"))
      return paramString1; 
    if (paramString2.contains("!") || paramString2.contains(":"))
      return g(paramString2, str); 
    if (paramString2.endsWith(".apk"))
      return paramString2; 
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append(paramString1);
    stringBuilder.append(l.a(paramArrayOfbyte));
    stringBuilder.append(paramString2);
    return stringBuilder.toString();
  }
  
  public static int k(int paramInt) {
    return y(paramInt * 2) / 8;
  }
  
  public static int l(int paramInt1, int paramInt2, int paramInt3) {
    if (paramInt1 != 1) {
      if (paramInt1 != 2) {
        if (paramInt1 == 4)
          return paramInt2 + paramInt3; 
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("Unexpected flag: ");
        stringBuilder.append(paramInt1);
        throw c.c(stringBuilder.toString());
      } 
      return paramInt2;
    } 
    throw c.c("HOT methods are not stored in the bitmap");
  }
  
  public static int[] m(InputStream paramInputStream, int paramInt) {
    int[] arrayOfInt = new int[paramInt];
    byte b = 0;
    int i = 0;
    while (b < paramInt) {
      i += c.h(paramInputStream);
      arrayOfInt[b] = i;
      b++;
    } 
    return arrayOfInt;
  }
  
  public static int n(BitSet paramBitSet, int paramInt1, int paramInt2) {
    byte b = 2;
    if (!paramBitSet.get(l(2, paramInt1, paramInt2)))
      b = 0; 
    int i = b;
    if (paramBitSet.get(l(4, paramInt1, paramInt2)))
      i = b | 0x4; 
    return i;
  }
  
  public static byte[] o(InputStream paramInputStream, byte[] paramArrayOfbyte) {
    if (Arrays.equals(paramArrayOfbyte, c.d(paramInputStream, paramArrayOfbyte.length)))
      return c.d(paramInputStream, l.b.length); 
    throw c.c("Invalid magic");
  }
  
  public static void p(InputStream paramInputStream, b paramb) {
    int j = paramInputStream.available() - paramb.f;
    int i = 0;
    label13: while (paramInputStream.available() > j) {
      int n = i + c.h(paramInputStream);
      paramb.i.put(Integer.valueOf(n), Integer.valueOf(1));
      int m = c.h(paramInputStream);
      while (true) {
        i = n;
        if (m > 0) {
          A(paramInputStream);
          m--;
          continue;
        } 
        continue label13;
      } 
    } 
    if (paramInputStream.available() == j)
      return; 
    throw c.c("Read too much data during profile line parse");
  }
  
  public static b[] q(InputStream paramInputStream, byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2, b[] paramArrayOfb) {
    if (Arrays.equals(paramArrayOfbyte1, l.f)) {
      if (!Arrays.equals(l.a, paramArrayOfbyte2))
        return r(paramInputStream, paramArrayOfbyte1, paramArrayOfb); 
      throw c.c("Requires new Baseline Profile Metadata. Please rebuild the APK with Android Gradle Plugin 7.2 Canary 7 or higher");
    } 
    if (Arrays.equals(paramArrayOfbyte1, l.g))
      return t(paramInputStream, paramArrayOfbyte2, paramArrayOfb); 
    throw c.c("Unsupported meta version");
  }
  
  public static b[] r(InputStream paramInputStream, byte[] paramArrayOfbyte, b[] paramArrayOfb) {
    if (Arrays.equals(paramArrayOfbyte, l.f)) {
      int i = c.j(paramInputStream);
      long l = c.i(paramInputStream);
      paramArrayOfbyte = c.e(paramInputStream, (int)c.i(paramInputStream), (int)l);
      if (paramInputStream.read() <= 0) {
        paramInputStream = new ByteArrayInputStream(paramArrayOfbyte);
        try {
          return s(paramInputStream, i, paramArrayOfb);
        } finally {
          try {
            paramInputStream.close();
          } finally {
            paramInputStream = null;
          } 
        } 
      } 
      throw c.c("Content found after the end of file");
    } 
    throw c.c("Unsupported meta version");
  }
  
  public static b[] s(InputStream paramInputStream, int paramInt, b[] paramArrayOfb) {
    int i = paramInputStream.available();
    boolean bool = false;
    if (i == 0)
      return new b[0]; 
    if (paramInt == paramArrayOfb.length) {
      String[] arrayOfString = new String[paramInt];
      int[] arrayOfInt = new int[paramInt];
      int j = 0;
      while (true) {
        i = bool;
        if (j < paramInt) {
          i = c.h(paramInputStream);
          arrayOfInt[j] = c.h(paramInputStream);
          arrayOfString[j] = c.f(paramInputStream, i);
          j++;
          continue;
        } 
        break;
      } 
      while (i < paramInt) {
        b b1 = paramArrayOfb[i];
        if (b1.b.equals(arrayOfString[i])) {
          j = arrayOfInt[i];
          b1.e = j;
          b1.h = m(paramInputStream, j);
          i++;
          continue;
        } 
        throw c.c("Order of dexfiles in metadata did not match baseline");
      } 
      return paramArrayOfb;
    } 
    throw c.c("Mismatched number of dex files found in metadata");
  }
  
  public static b[] t(InputStream paramInputStream, byte[] paramArrayOfbyte, b[] paramArrayOfb) {
    int i = c.h(paramInputStream);
    long l = c.i(paramInputStream);
    byte[] arrayOfByte = c.e(paramInputStream, (int)c.i(paramInputStream), (int)l);
    if (paramInputStream.read() <= 0) {
      paramInputStream = new ByteArrayInputStream(arrayOfByte);
      try {
        return u(paramInputStream, paramArrayOfbyte, i, paramArrayOfb);
      } finally {
        try {
          paramInputStream.close();
        } finally {
          paramInputStream = null;
        } 
      } 
    } 
    throw c.c("Content found after the end of file");
  }
  
  public static b[] u(InputStream paramInputStream, byte[] paramArrayOfbyte, int paramInt, b[] paramArrayOfb) {
    int i = paramInputStream.available();
    byte b1 = 0;
    if (i == 0)
      return new b[0]; 
    if (paramInt == paramArrayOfb.length) {
      while (b1 < paramInt) {
        int[] arrayOfInt;
        c.h(paramInputStream);
        String str = c.f(paramInputStream, c.h(paramInputStream));
        long l = c.i(paramInputStream);
        i = c.h(paramInputStream);
        b b2 = i(paramArrayOfb, str);
        if (b2 != null) {
          b2.d = l;
          arrayOfInt = m(paramInputStream, i);
          if (Arrays.equals(paramArrayOfbyte, l.e)) {
            b2.e = i;
            b2.h = arrayOfInt;
          } 
          b1++;
          continue;
        } 
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("Missing profile key: ");
        stringBuilder.append((String)arrayOfInt);
        throw c.c(stringBuilder.toString());
      } 
      return paramArrayOfb;
    } 
    throw c.c("Mismatched number of dex files found in metadata");
  }
  
  public static void v(InputStream paramInputStream, b paramb) {
    BitSet bitSet = BitSet.valueOf(c.d(paramInputStream, c.a(paramb.g * 2)));
    byte b1 = 0;
    while (true) {
      int i = paramb.g;
      if (b1 < i) {
        i = n(bitSet, b1, i);
        if (i != 0) {
          Integer integer2 = paramb.i.get(Integer.valueOf(b1));
          Integer integer1 = integer2;
          if (integer2 == null)
            integer1 = Integer.valueOf(0); 
          paramb.i.put(Integer.valueOf(b1), Integer.valueOf(i | integer1.intValue()));
        } 
        b1++;
        continue;
      } 
      break;
    } 
  }
  
  public static b[] w(InputStream paramInputStream, byte[] paramArrayOfbyte, String paramString) {
    if (Arrays.equals(paramArrayOfbyte, l.b)) {
      int i = c.j(paramInputStream);
      long l = c.i(paramInputStream);
      paramArrayOfbyte = c.e(paramInputStream, (int)c.i(paramInputStream), (int)l);
      if (paramInputStream.read() <= 0) {
        paramInputStream = new ByteArrayInputStream(paramArrayOfbyte);
        try {
          return x(paramInputStream, paramString, i);
        } finally {
          try {
            paramInputStream.close();
          } finally {
            paramInputStream = null;
          } 
        } 
      } 
      throw c.c("Content found after the end of file");
    } 
    throw c.c("Unsupported version");
  }
  
  public static b[] x(InputStream paramInputStream, String paramString, int paramInt) {
    int j;
    int i = paramInputStream.available();
    byte b = 0;
    if (i == 0)
      return new b[0]; 
    b[] arrayOfB = new b[paramInt];
    i = 0;
    while (true) {
      j = b;
      if (i < paramInt) {
        int m = c.h(paramInputStream);
        j = c.h(paramInputStream);
        long l2 = c.i(paramInputStream);
        long l3 = c.i(paramInputStream);
        long l1 = c.i(paramInputStream);
        arrayOfB[i] = new b(paramString, c.f(paramInputStream, m), l3, 0L, j, (int)l2, (int)l1, new int[j], new TreeMap<Integer, Integer>());
        i++;
        continue;
      } 
      break;
    } 
    while (j < paramInt) {
      b b1 = arrayOfB[j];
      p(paramInputStream, b1);
      b1.h = m(paramInputStream, b1.e);
      v(paramInputStream, b1);
      j++;
    } 
    return arrayOfB;
  }
  
  public static int y(int paramInt) {
    return paramInt + 8 - 1 & 0xFFFFFFF8;
  }
  
  public static void z(byte[] paramArrayOfbyte, int paramInt1, int paramInt2, b paramb) {
    paramInt2 = l(paramInt1, paramInt2, paramb.g);
    paramInt1 = paramInt2 / 8;
    paramArrayOfbyte[paramInt1] = (byte)(1 << paramInt2 % 8 | paramArrayOfbyte[paramInt1]);
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a5\k.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */