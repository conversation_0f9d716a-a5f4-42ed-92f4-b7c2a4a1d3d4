package aa;

import b9.e;
import ta.k;
import ta.n;
import ta.o0;
import ta.q;
import ta.r;
import u8.q1;

public final class m extends f {
  public final g j;
  
  public g.b k;
  
  public long l;
  
  public volatile boolean m;
  
  public m(n paramn, r paramr, q1 paramq1, int paramInt, Object paramObject, g paramg) {
    super(paramn, paramr, 2, paramq1, paramInt, paramObject, -9223372036854775807L, -9223372036854775807L);
    this.j = paramg;
  }
  
  public void a() {
    if (this.l == 0L)
      this.j.e(this.k, -9223372036854775807L, -9223372036854775807L); 
    try {
      null = this.b.e(this.l);
      e e = new e();
      o0 o0 = this.i;
      this((k)o0, null.g, o0.e(null));
    } finally {
      q.a((n)this.i);
    } 
  }
  
  public void b() {
    this.m = true;
  }
  
  public void g(g.b paramb) {
    this.k = paramb;
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\aa\m.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */