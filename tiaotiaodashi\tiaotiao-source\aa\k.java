package aa;

import b9.e;
import ta.n;
import ta.o0;
import ta.q;
import ta.r;
import u8.q1;

public class k extends a {
  public final int o;
  
  public final long p;
  
  public final g q;
  
  public long r;
  
  public volatile boolean s;
  
  public boolean t;
  
  public k(n paramn, r paramr, q1 paramq1, int paramInt1, Object paramObject, long paramLong1, long paramLong2, long paramLong3, long paramLong4, long paramLong5, int paramInt2, long paramLong6, g paramg) {
    super(paramn, paramr, paramq1, paramInt1, paramObject, paramLong1, paramLong2, paramLong3, paramLong4, paramLong5);
    this.o = paramInt2;
    this.p = paramLong6;
    this.q = paramg;
  }
  
  public final void a() {
    if (this.r == 0L) {
      c c = j();
      c.c(this.p);
      g g1 = this.q;
      g.b b = l(c);
      long l1 = this.k;
      if (l1 == -9223372036854775807L) {
        l1 = -9223372036854775807L;
      } else {
        l1 -= this.p;
      } 
      long l2 = this.l;
      if (l2 == -9223372036854775807L) {
        l2 = -9223372036854775807L;
      } else {
        l2 -= this.p;
      } 
      g1.e(b, l1, l2);
    } 
    try {
      r r = this.b.e(this.r);
      e e = new e();
      null = this.i;
      this((ta.k)null, r.g, null.e(r));
    } finally {
      q.a((n)this.i);
    } 
  }
  
  public final void b() {
    this.s = true;
  }
  
  public long g() {
    return this.j + this.o;
  }
  
  public boolean h() {
    return this.t;
  }
  
  public g.b l(c paramc) {
    return paramc;
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\aa\k.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */