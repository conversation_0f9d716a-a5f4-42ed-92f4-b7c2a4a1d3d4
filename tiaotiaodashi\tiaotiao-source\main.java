import java.util.*;
import java.util.concurrent.*;
import java.io.*;

/**
 * AI跳舞功能核心实现
 * 基于MediaPipe姿态检测和余弦相似度算法的舞蹈评分系统
 */
public class main {
    
    // ==================== 数据结构定义 ====================
    
    /**
     * 姿态关键点数据结构
     */
    static class PoseLandmark {
        public float x, y, z;           // 3D坐标
        public float visibility;        // 可见性 [0-1]
        public float presence;          // 存在性 [0-1]
        public long timestamp;          // 时间戳
        
        public PoseLandmark(float x, float y, float z, float visibility, float presence, long timestamp) {
            this.x = x; this.y = y; this.z = z;
            this.visibility = visibility;
            this.presence = presence;
            this.timestamp = timestamp;
        }
        
        @Override
        public String toString() {
            return String.format("Point(%.3f,%.3f,%.3f) vis:%.2f pres:%.2f", x, y, z, visibility, presence);
        }
    }
    
    /**
     * 完整姿态数据（33个关键点）
     */
    static class PoseData {
        public static final int POSE_LANDMARKS_COUNT = 33;
        public List<PoseLandmark> landmarks;
        public long timestamp;
        
        public PoseData(long timestamp) {
            this.landmarks = new ArrayList<>(POSE_LANDMARKS_COUNT);
            this.timestamp = timestamp;
        }
        
        public void addLandmark(PoseLandmark landmark) {
            landmarks.add(landmark);
        }
        
        public boolean isComplete() {
            return landmarks.size() == POSE_LANDMARKS_COUNT;
        }
    }
    
    /**
     * 舞蹈动作序列
     */
    static class DanceSequence {
        public List<PoseData> poses;
        public String danceName;
        public long duration;
        
        public DanceSequence(String danceName) {
            this.danceName = danceName;
            this.poses = new ArrayList<>();
        }
        
        public void addPose(PoseData pose) {
            poses.add(pose);
            if (poses.size() == 1) {
                duration = 0;
            } else {
                duration = pose.timestamp - poses.get(0).timestamp;
            }
        }
    }
    
    // ==================== 核心算法实现 ====================
    
    /**
     * 余弦相似度计算器
     */
    static class CosineSimilarityCalculator {
        
        /**
         * 计算两个姿态的相似度
         */
        public static double calculatePoseSimilarity(PoseData pose1, PoseData pose2) {
            if (!pose1.isComplete() || !pose2.isComplete()) {
                return 0.0;
            }
            
            // 将姿态转换为特征向量
            float[] vector1 = poseToVector(pose1);
            float[] vector2 = poseToVector(pose2);
            
            return computeCosineSimilarity(vector1, vector2);
        }
        
        /**
         * 将姿态数据转换为特征向量
         */
        private static float[] poseToVector(PoseData pose) {
            // 每个关键点3个坐标 + 可见性权重 = 4个特征
            float[] vector = new float[pose.landmarks.size() * 4];
            
            for (int i = 0; i < pose.landmarks.size(); i++) {
                PoseLandmark landmark = pose.landmarks.get(i);
                int baseIndex = i * 4;
                
                // 坐标归一化处理
                vector[baseIndex] = landmark.x;
                vector[baseIndex + 1] = landmark.y;
                vector[baseIndex + 2] = landmark.z;
                // 可见性作为权重
                vector[baseIndex + 3] = landmark.visibility * landmark.presence;
            }
            
            return vector;
        }
        
        /**
         * 计算余弦相似度
         */
        private static double computeCosineSimilarity(float[] vector1, float[] vector2) {
            if (vector1.length != vector2.length) {
                throw new IllegalArgumentException("向量维度不匹配");
            }
            
            double dotProduct = 0.0;
            double norm1 = 0.0;
            double norm2 = 0.0;
            
            for (int i = 0; i < vector1.length; i++) {
                dotProduct += vector1[i] * vector2[i];
                norm1 += vector1[i] * vector1[i];
                norm2 += vector2[i] * vector2[i];
            }
            
            if (norm1 == 0.0 || norm2 == 0.0) {
                return 0.0;
            }
            
            return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
        }
    }
    
    /**
     * 舞蹈评分系统
     */
    static class DanceScorer {
        private static final double EXCELLENT_THRESHOLD = 0.9;
        private static final double GOOD_THRESHOLD = 0.75;
        private static final double FAIR_THRESHOLD = 0.6;
        
        /**
         * 评估舞蹈表现
         */
        public static DanceScore evaluatePerformance(DanceSequence userDance, DanceSequence standardDance) {
            if (userDance.poses.isEmpty() || standardDance.poses.isEmpty()) {
                return new DanceScore(0.0, "无效数据");
            }
            
            List<Double> similarities = new ArrayList<>();
            int minSize = Math.min(userDance.poses.size(), standardDance.poses.size());
            
            // 逐帧比较
            for (int i = 0; i < minSize; i++) {
                double similarity = CosineSimilarityCalculator.calculatePoseSimilarity(
                    userDance.poses.get(i), 
                    standardDance.poses.get(i)
                );
                similarities.add(similarity);
            }
            
            // 计算平均相似度
            double averageSimilarity = similarities.stream()
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0.0);
            
            // 转换为百分制分数
            double score = averageSimilarity * 100;
            String grade = getGrade(averageSimilarity);
            
            return new DanceScore(score, grade, similarities);
        }
        
        private static String getGrade(double similarity) {
            if (similarity >= EXCELLENT_THRESHOLD) return "优秀";
            if (similarity >= GOOD_THRESHOLD) return "良好";
            if (similarity >= FAIR_THRESHOLD) return "及格";
            return "需要改进";
        }
    }
    
    /**
     * 评分结果
     */
    static class DanceScore {
        public double score;
        public String grade;
        public List<Double> frameSimilarities;
        
        public DanceScore(double score, String grade) {
            this.score = score;
            this.grade = grade;
        }
        
        public DanceScore(double score, String grade, List<Double> frameSimilarities) {
            this.score = score;
            this.grade = grade;
            this.frameSimilarities = frameSimilarities;
        }
        
        @Override
        public String toString() {
            return String.format("得分: %.1f分 | 等级: %s", score, grade);
        }
    }
    
    // ==================== 模拟数据生成 ====================
    
    /**
     * 生成模拟姿态数据
     */
    static class MockDataGenerator {
        private static final Random random = new Random();
        
        public static PoseData generateRandomPose(long timestamp) {
            PoseData pose = new PoseData(timestamp);
            
            for (int i = 0; i < PoseData.POSE_LANDMARKS_COUNT; i++) {
                float x = random.nextFloat();
                float y = random.nextFloat();
                float z = random.nextFloat() * 0.5f; // z坐标范围较小
                float visibility = 0.8f + random.nextFloat() * 0.2f; // 高可见性
                float presence = 0.9f + random.nextFloat() * 0.1f;   // 高存在性
                
                pose.addLandmark(new PoseLandmark(x, y, z, visibility, presence, timestamp));
            }
            
            return pose;
        }
        
        public static DanceSequence generateStandardDance(String danceName, int frameCount) {
            DanceSequence dance = new DanceSequence(danceName);
            long baseTime = System.currentTimeMillis();
            
            for (int i = 0; i < frameCount; i++) {
                long timestamp = baseTime + i * 33; // 30fps
                dance.addPose(generateRandomPose(timestamp));
            }
            
            return dance;
        }
        
        public static DanceSequence generateUserDance(DanceSequence standard, double similarity) {
            DanceSequence userDance = new DanceSequence("用户表演");
            
            for (PoseData standardPose : standard.poses) {
                PoseData userPose = new PoseData(standardPose.timestamp);
                
                for (PoseLandmark standardLandmark : standardPose.landmarks) {
                    // 根据相似度添加噪声
                    float noise = (float)((1.0 - similarity) * 0.1);
                    float x = standardLandmark.x + (random.nextFloat() - 0.5f) * noise;
                    float y = standardLandmark.y + (random.nextFloat() - 0.5f) * noise;
                    float z = standardLandmark.z + (random.nextFloat() - 0.5f) * noise;
                    
                    userPose.addLandmark(new PoseLandmark(
                        x, y, z, 
                        standardLandmark.visibility, 
                        standardLandmark.presence, 
                        standardLandmark.timestamp
                    ));
                }
                
                userDance.addPose(userPose);
            }
            
            return userDance;
        }
    }
    
    // ==================== 高级功能 ====================

    /**
     * 动作纠正建议系统
     */
    static class MotionCorrectionSystem {

        /**
         * 分析动作差异并提供建议
         */
        public static List<String> analyzeAndSuggest(PoseData userPose, PoseData standardPose) {
            List<String> suggestions = new ArrayList<>();

            // 关键身体部位索引（基于MediaPipe Pose模型）
            int[] headPoints = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10}; // 头部和面部
            int[] armPoints = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22}; // 手臂
            int[] legPoints = {23, 24, 25, 26, 27, 28, 29, 30, 31, 32}; // 腿部

            // 分析各部位差异
            double headDiff = calculatePartDifference(userPose, standardPose, headPoints);
            double armDiff = calculatePartDifference(userPose, standardPose, armPoints);
            double legDiff = calculatePartDifference(userPose, standardPose, legPoints);

            // 生成建议
            if (headDiff > 0.15) {
                suggestions.add("调整头部姿态，保持头部稳定");
            }
            if (armDiff > 0.2) {
                suggestions.add("手臂动作需要更加标准，注意手臂的伸展角度");
            }
            if (legDiff > 0.25) {
                suggestions.add("腿部动作不够到位，注意步伐的准确性");
            }

            if (suggestions.isEmpty()) {
                suggestions.add("动作很标准，继续保持！");
            }

            return suggestions;
        }

        private static double calculatePartDifference(PoseData user, PoseData standard, int[] points) {
            double totalDiff = 0.0;
            int validPoints = 0;

            for (int pointIndex : points) {
                if (pointIndex < user.landmarks.size() && pointIndex < standard.landmarks.size()) {
                    PoseLandmark userPoint = user.landmarks.get(pointIndex);
                    PoseLandmark standardPoint = standard.landmarks.get(pointIndex);

                    // 计算3D欧几里得距离
                    double diff = Math.sqrt(
                        Math.pow(userPoint.x - standardPoint.x, 2) +
                        Math.pow(userPoint.y - standardPoint.y, 2) +
                        Math.pow(userPoint.z - standardPoint.z, 2)
                    );

                    totalDiff += diff;
                    validPoints++;
                }
            }

            return validPoints > 0 ? totalDiff / validPoints : 0.0;
        }
    }

    /**
     * 舞蹈数据分析器
     */
    static class DanceAnalyzer {

        /**
         * 分析舞蹈节奏
         */
        public static RhythmAnalysis analyzeRhythm(DanceSequence dance) {
            if (dance.poses.size() < 2) {
                return new RhythmAnalysis(0.0, "数据不足");
            }

            List<Double> intervals = new ArrayList<>();
            for (int i = 1; i < dance.poses.size(); i++) {
                long interval = dance.poses.get(i).timestamp - dance.poses.get(i-1).timestamp;
                intervals.add((double)interval);
            }

            // 计算节奏稳定性（标准差）
            double mean = intervals.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double variance = intervals.stream()
                .mapToDouble(interval -> Math.pow(interval - mean, 2))
                .average().orElse(0.0);
            double stability = Math.sqrt(variance);

            String rhythmQuality;
            if (stability < 5.0) rhythmQuality = "节奏非常稳定";
            else if (stability < 10.0) rhythmQuality = "节奏较稳定";
            else if (stability < 20.0) rhythmQuality = "节奏一般";
            else rhythmQuality = "节奏不稳定";

            return new RhythmAnalysis(stability, rhythmQuality);
        }

        /**
         * 计算动作流畅度
         */
        public static double calculateSmoothness(DanceSequence dance) {
            if (dance.poses.size() < 3) return 0.0;

            double totalSmoothness = 0.0;
            int validTransitions = 0;

            for (int i = 1; i < dance.poses.size() - 1; i++) {
                PoseData prev = dance.poses.get(i-1);
                PoseData curr = dance.poses.get(i);
                PoseData next = dance.poses.get(i+1);

                // 计算相邻帧之间的相似度变化
                double sim1 = CosineSimilarityCalculator.calculatePoseSimilarity(prev, curr);
                double sim2 = CosineSimilarityCalculator.calculatePoseSimilarity(curr, next);

                // 流畅度 = 1 - |相似度变化|
                double smoothness = 1.0 - Math.abs(sim1 - sim2);
                totalSmoothness += smoothness;
                validTransitions++;
            }

            return validTransitions > 0 ? totalSmoothness / validTransitions : 0.0;
        }
    }

    /**
     * 节奏分析结果
     */
    static class RhythmAnalysis {
        public double stability;
        public String quality;

        public RhythmAnalysis(double stability, String quality) {
            this.stability = stability;
            this.quality = quality;
        }

        @Override
        public String toString() {
            return String.format("节奏稳定性: %.2f | %s", stability, quality);
        }
    }

    // ==================== 主程序 ====================

    public static void main(String[] args) {
        System.out.println("=== AI跳舞功能完整演示系统 ===\n");

        // 1. 生成标准舞蹈动作
        System.out.println("1. 加载标准舞蹈动作...");
        DanceSequence standardDance = MockDataGenerator.generateStandardDance("经典舞蹈", 30);
        System.out.printf("   标准舞蹈包含 %d 帧动作，持续时间: %d ms\n\n",
            standardDance.poses.size(), standardDance.duration);

        // 2. 模拟不同水平的用户表演
        double[] performanceLevels = {0.95, 0.85, 0.70, 0.50};
        String[] levelNames = {"专业级", "熟练级", "初学级", "入门级"};

        System.out.println("2. 评估不同水平的舞蹈表演:");
        System.out.println("   " + "=".repeat(70));

        for (int i = 0; i < performanceLevels.length; i++) {
            // 生成用户舞蹈数据
            DanceSequence userDance = MockDataGenerator.generateUserDance(
                standardDance, performanceLevels[i]);

            // 基础评分
            DanceScore score = DanceScorer.evaluatePerformance(userDance, standardDance);
            System.out.printf("   %s表演: %s\n", levelNames[i], score);

            // 节奏分析
            RhythmAnalysis rhythm = DanceAnalyzer.analyzeRhythm(userDance);
            System.out.printf("   %s\n", rhythm);

            // 流畅度分析
            double smoothness = DanceAnalyzer.calculateSmoothness(userDance);
            System.out.printf("   动作流畅度: %.1f%%\n", smoothness * 100);

            // 动作建议（仅显示第一帧的建议）
            if (!userDance.poses.isEmpty() && !standardDance.poses.isEmpty()) {
                List<String> suggestions = MotionCorrectionSystem.analyzeAndSuggest(
                    userDance.poses.get(0), standardDance.poses.get(0));
                System.out.printf("   改进建议: %s\n", suggestions.get(0));
            }

            System.out.println();
        }

        // 3. 实时评分演示
        System.out.println("3. 实时评分演示:");
        System.out.println("   " + "=".repeat(70));
        simulateRealTimeScoring(standardDance);

        // 4. 详细分析演示
        System.out.println("\n4. 详细舞蹈分析:");
        System.out.println("   " + "=".repeat(70));
        demonstrateDetailedAnalysis(standardDance);

        System.out.println("\n=== 演示完成 ===");
        System.out.println("\n核心技术总结:");
        System.out.println("• MediaPipe姿态检测 - 33个关键点实时追踪");
        System.out.println("• 余弦相似度算法 - 动作相似度计算");
        System.out.println("• 时间序列分析 - 节奏和流畅度评估");
        System.out.println("• 智能纠错系统 - 个性化改进建议");
    }
    
    /**
     * 模拟实时评分过程
     */
    private static void simulateRealTimeScoring(DanceSequence standardDance) {
        System.out.println("   模拟实时舞蹈评分中...");

        double totalScore = 0.0;
        List<String> realtimeFeedback = new ArrayList<>();

        for (int i = 0; i < Math.min(10, standardDance.poses.size()); i++) {
            // 模拟用户当前姿态
            PoseData currentUserPose = MockDataGenerator.generateRandomPose(System.currentTimeMillis());
            PoseData standardPose = standardDance.poses.get(i);

            // 计算实时相似度
            double similarity = CosineSimilarityCalculator.calculatePoseSimilarity(
                currentUserPose, standardPose);

            totalScore += similarity;

            // 显示进度条和分数
            String progressBar = generateProgressBar(similarity, 20);
            System.out.printf("   帧 %2d: %s %.1f%%",
                i + 1, progressBar, similarity * 100);

            // 实时反馈
            if (similarity > 0.9) {
                System.out.print(" 🌟 完美!");
                realtimeFeedback.add("完美");
            } else if (similarity > 0.75) {
                System.out.print(" ✅ 很好!");
                realtimeFeedback.add("很好");
            } else if (similarity > 0.6) {
                System.out.print(" ⚠️  需要调整");
                realtimeFeedback.add("需要调整");
            } else {
                System.out.print(" ❌ 动作不准确");
                realtimeFeedback.add("不准确");
            }

            System.out.println();

            // 模拟处理延迟
            try {
                Thread.sleep(150);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        // 显示实时评分总结
        double avgScore = totalScore / Math.min(10, standardDance.poses.size());
        System.out.printf("   实时评分平均值: %.1f%% | 反馈统计: %s\n",
            avgScore * 100, analyzeFeedback(realtimeFeedback));
    }

    /**
     * 分析反馈统计
     */
    private static String analyzeFeedback(List<String> feedback) {
        Map<String, Integer> counts = new HashMap<>();
        for (String f : feedback) {
            counts.put(f, counts.getOrDefault(f, 0) + 1);
        }

        StringBuilder result = new StringBuilder();
        counts.forEach((key, value) -> {
            if (result.length() > 0) result.append(", ");
            result.append(key).append("×").append(value);
        });

        return result.toString();
    }

    /**
     * 详细分析演示
     */
    private static void demonstrateDetailedAnalysis(DanceSequence standardDance) {
        // 生成一个中等水平的用户表演
        DanceSequence userDance = MockDataGenerator.generateUserDance(standardDance, 0.75);

        System.out.println("   分析用户舞蹈表演的各个维度:");

        // 1. 整体评分
        DanceScore overallScore = DanceScorer.evaluatePerformance(userDance, standardDance);
        System.out.printf("   • 整体评分: %s\n", overallScore);

        // 2. 节奏分析
        RhythmAnalysis rhythm = DanceAnalyzer.analyzeRhythm(userDance);
        System.out.printf("   • 节奏分析: %s\n", rhythm);

        // 3. 流畅度分析
        double smoothness = DanceAnalyzer.calculateSmoothness(userDance);
        System.out.printf("   • 动作流畅度: %.1f%%\n", smoothness * 100);

        // 4. 关键帧分析
        System.out.println("   • 关键帧分析:");
        int[] keyFrames = {0, userDance.poses.size()/4, userDance.poses.size()/2,
                          3*userDance.poses.size()/4, userDance.poses.size()-1};

        for (int frameIndex : keyFrames) {
            if (frameIndex < userDance.poses.size() && frameIndex < standardDance.poses.size()) {
                double frameSimilarity = CosineSimilarityCalculator.calculatePoseSimilarity(
                    userDance.poses.get(frameIndex), standardDance.poses.get(frameIndex));

                List<String> suggestions = MotionCorrectionSystem.analyzeAndSuggest(
                    userDance.poses.get(frameIndex), standardDance.poses.get(frameIndex));

                System.out.printf("     帧 %d: %.1f%% - %s\n",
                    frameIndex + 1, frameSimilarity * 100, suggestions.get(0));
            }
        }

        // 5. 改进建议总结
        System.out.println("   • 综合改进建议:");
        if (overallScore.score >= 85) {
            System.out.println("     - 表现优秀，继续保持当前水平");
            System.out.println("     - 可以尝试更复杂的舞蹈动作");
        } else if (overallScore.score >= 70) {
            System.out.println("     - 基础动作掌握良好，需要提高精确度");
            System.out.println("     - 注意动作的细节和表现力");
        } else if (overallScore.score >= 60) {
            System.out.println("     - 需要加强基础动作练习");
            System.out.println("     - 建议跟随慢速教学视频练习");
        } else {
            System.out.println("     - 建议从基础动作开始学习");
            System.out.println("     - 多观看标准动作示范");
        }

        // 6. 技术指标总结
        System.out.println("   • 技术指标:");
        System.out.printf("     - 姿态检测精度: 33个关键点\n");
        System.out.printf("     - 处理帧率: 30 FPS\n");
        System.out.printf("     - 相似度算法: 余弦相似度\n");
        System.out.printf("     - 分析维度: 4个（整体、节奏、流畅度、关键帧）\n");
    }

    /**
     * 生成进度条
     */
    private static String generateProgressBar(double percentage, int length) {
        int filled = (int)(percentage * length);
        StringBuilder bar = new StringBuilder("[");

        for (int i = 0; i < length; i++) {
            if (i < filled) {
                bar.append("█");
            } else {
                bar.append("░");
            }
        }

        bar.append("]");
        return bar.toString();
    }
}
