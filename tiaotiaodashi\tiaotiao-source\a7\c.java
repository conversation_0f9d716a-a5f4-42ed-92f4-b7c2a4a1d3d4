package a7;

import android.content.Context;
import android.net.Uri;
import c7.g0;
import com.bumptech.glide.load.data.d;
import java.io.InputStream;
import o7.d;
import t6.f;
import t6.h;
import u6.b;
import z6.o;
import z6.p;
import z6.s;

public class c implements o<Uri, InputStream> {
  public final Context a;
  
  public c(Context paramContext) {
    this.a = paramContext.getApplicationContext();
  }
  
  public o.a<InputStream> c(Uri paramUri, int paramInt1, int paramInt2, h paramh) {
    return (b.d(paramInt1, paramInt2) && e(paramh)) ? new o.a((f)new d(paramUri), (d)u6.c.g(this.a, paramUri)) : null;
  }
  
  public boolean d(Uri paramUri) {
    return b.c(paramUri);
  }
  
  public final boolean e(h paramh) {
    boolean bool;
    Long long_ = (Long)paramh.c(g0.d);
    if (long_ != null && long_.longValue() == -1L) {
      bool = true;
    } else {
      bool = false;
    } 
    return bool;
  }
  
  public static class a implements p<Uri, InputStream> {
    public final Context a;
    
    public a(Context param1Context) {
      this.a = param1Context;
    }
    
    public o<Uri, InputStream> c(s param1s) {
      return new c(this.a);
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a7\c.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */