package aa;

import java.util.NoSuchElementException;

public abstract class b implements o {
  public final long b;
  
  public final long c;
  
  public long d;
  
  public b(long paramLong1, long paramLong2) {
    this.b = paramLong1;
    this.c = paramLong2;
    f();
  }
  
  public final void c() {
    long l = this.d;
    if (l >= this.b && l <= this.c)
      return; 
    throw new NoSuchElementException();
  }
  
  public final long d() {
    return this.d;
  }
  
  public boolean e() {
    boolean bool;
    if (this.d > this.c) {
      bool = true;
    } else {
      bool = false;
    } 
    return bool;
  }
  
  public void f() {
    this.d = this.b - 1L;
  }
  
  public boolean next() {
    this.d++;
    return e() ^ true;
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\aa\b.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */