package aa;

import ta.n;
import ta.r;
import u8.q1;

public abstract class a extends n {
  public final long k;
  
  public final long l;
  
  public c m;
  
  public int[] n;
  
  public a(n paramn, r paramr, q1 paramq1, int paramInt, Object paramObject, long paramLong1, long paramLong2, long paramLong3, long paramLong4, long paramLong5) {
    super(paramn, paramr, paramq1, paramInt, paramObject, paramLong1, paramLong2, paramLong5);
    this.k = paramLong3;
    this.l = paramLong4;
  }
  
  public final int i(int paramInt) {
    return ((int[])va.a.h(this.n))[paramInt];
  }
  
  public final c j() {
    return (c)va.a.h(this.m);
  }
  
  public void k(c paramc) {
    this.m = paramc;
    this.n = paramc.a();
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\aa\a.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */