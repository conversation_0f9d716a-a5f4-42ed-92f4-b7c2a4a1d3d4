package android.support.v4.media;

import android.os.Bundle;
import android.os.Parcelable;
import android.support.v4.media.session.MediaSessionCompat;
import c.b;
import java.util.ArrayList;

class MediaBrowserCompat$SearchResultReceiver extends b {
  public void c(int paramInt, Bundle paramBundle) {
    Bundle bundle = paramBundle;
    if (paramBundle != null)
      bundle = MediaSessionCompat.n(paramBundle); 
    if (paramInt == 0 && bundle != null && bundle.containsKey("search_results")) {
      Parcelable[] arrayOfParcelable = bundle.getParcelableArray("search_results");
      arrayOfParcelable.getClass();
      ArrayList<MediaBrowserCompat$MediaItem> arrayList = new ArrayList();
      int i = arrayOfParcelable.length;
      for (paramInt = 0; paramInt < i; paramInt++)
        arrayList.add((MediaBrowserCompat$MediaItem)arrayOfParcelable[paramInt]); 
      throw null;
    } 
    throw null;
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\android\support\v4\media\MediaBrowserCompat$SearchResultReceiver.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */