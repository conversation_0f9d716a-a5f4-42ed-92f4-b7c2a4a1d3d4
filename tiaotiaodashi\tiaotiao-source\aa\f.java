package aa;

import android.net.Uri;
import java.util.List;
import java.util.Map;
import ta.g0;
import ta.n;
import ta.o0;
import ta.r;
import u8.q1;
import va.a;
import y9.o;

public abstract class f implements g0.e {
  public final long a;
  
  public final r b;
  
  public final int c;
  
  public final q1 d;
  
  public final int e;
  
  public final Object f;
  
  public final long g;
  
  public final long h;
  
  public final o0 i;
  
  public f(n paramn, r paramr, int paramInt1, q1 paramq1, int paramInt2, Object paramObject, long paramLong1, long paramLong2) {
    this.i = new o0(paramn);
    this.b = (r)a.e(paramr);
    this.c = paramInt1;
    this.d = paramq1;
    this.e = paramInt2;
    this.f = paramObject;
    this.g = paramLong1;
    this.h = paramLong2;
    this.a = o.a();
  }
  
  public final long c() {
    return this.i.o();
  }
  
  public final long d() {
    return this.h - this.g;
  }
  
  public final Map<String, List<String>> e() {
    return this.i.q();
  }
  
  public final Uri f() {
    return this.i.p();
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\aa\f.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */