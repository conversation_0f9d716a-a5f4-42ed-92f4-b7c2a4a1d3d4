#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI跳舞功能核心实现演示
基于MediaPipe姿态检测和余弦相似度算法的舞蹈评分系统
"""

import numpy as np
import time
import random
from typing import List, Tuple, Dict
from dataclasses import dataclass
from math import sqrt

# ==================== 数据结构定义 ====================

@dataclass
class PoseLandmark:
    """姿态关键点数据结构"""
    x: float
    y: float
    z: float
    visibility: float  # 可见性 [0-1]
    presence: float    # 存在性 [0-1]
    timestamp: int     # 时间戳
    
    def __str__(self):
        return f"Point({self.x:.3f},{self.y:.3f},{self.z:.3f}) vis:{self.visibility:.2f} pres:{self.presence:.2f}"

@dataclass
class PoseData:
    """完整姿态数据（33个关键点）"""
    POSE_LANDMARKS_COUNT = 33
    
    def __init__(self, timestamp: int):
        self.landmarks: List[PoseLandmark] = []
        self.timestamp = timestamp
    
    def add_landmark(self, landmark: PoseLandmark):
        self.landmarks.append(landmark)
    
    def is_complete(self) -> bool:
        return len(self.landmarks) == self.POSE_LANDMARKS_COUNT

@dataclass
class DanceSequence:
    """舞蹈动作序列"""
    def __init__(self, dance_name: str):
        self.dance_name = dance_name
        self.poses: List[PoseData] = []
        self.duration = 0
    
    def add_pose(self, pose: PoseData):
        self.poses.append(pose)
        if len(self.poses) == 1:
            self.duration = 0
        else:
            self.duration = pose.timestamp - self.poses[0].timestamp

@dataclass
class DanceScore:
    """评分结果"""
    def __init__(self, score: float, grade: str, frame_similarities: List[float] = None):
        self.score = score
        self.grade = grade
        self.frame_similarities = frame_similarities or []
    
    def __str__(self):
        return f"得分: {self.score:.1f}分 | 等级: {self.grade}"

# ==================== 核心算法实现 ====================

class CosineSimilarityCalculator:
    """余弦相似度计算器"""
    
    @staticmethod
    def calculate_pose_similarity(pose1: PoseData, pose2: PoseData) -> float:
        """计算两个姿态的相似度"""
        if not pose1.is_complete() or not pose2.is_complete():
            return 0.0
        
        # 将姿态转换为特征向量
        vector1 = CosineSimilarityCalculator._pose_to_vector(pose1)
        vector2 = CosineSimilarityCalculator._pose_to_vector(pose2)
        
        return CosineSimilarityCalculator._compute_cosine_similarity(vector1, vector2)
    
    @staticmethod
    def _pose_to_vector(pose: PoseData) -> np.ndarray:
        """将姿态数据转换为特征向量"""
        # 每个关键点3个坐标 + 可见性权重 = 4个特征
        vector = np.zeros(len(pose.landmarks) * 4)
        
        for i, landmark in enumerate(pose.landmarks):
            base_index = i * 4
            # 坐标归一化处理
            vector[base_index] = landmark.x
            vector[base_index + 1] = landmark.y
            vector[base_index + 2] = landmark.z
            # 可见性作为权重
            vector[base_index + 3] = landmark.visibility * landmark.presence
        
        return vector
    
    @staticmethod
    def _compute_cosine_similarity(vector1: np.ndarray, vector2: np.ndarray) -> float:
        """计算余弦相似度"""
        if len(vector1) != len(vector2):
            raise ValueError("向量维度不匹配")
        
        dot_product = np.dot(vector1, vector2)
        norm1 = np.linalg.norm(vector1)
        norm2 = np.linalg.norm(vector2)
        
        if norm1 == 0.0 or norm2 == 0.0:
            return 0.0
        
        return dot_product / (norm1 * norm2)

class DanceScorer:
    """舞蹈评分系统"""
    EXCELLENT_THRESHOLD = 0.9
    GOOD_THRESHOLD = 0.75
    FAIR_THRESHOLD = 0.6
    
    @staticmethod
    def evaluate_performance(user_dance: DanceSequence, standard_dance: DanceSequence) -> DanceScore:
        """评估舞蹈表现"""
        if not user_dance.poses or not standard_dance.poses:
            return DanceScore(0.0, "无效数据")
        
        similarities = []
        min_size = min(len(user_dance.poses), len(standard_dance.poses))
        
        # 逐帧比较
        for i in range(min_size):
            similarity = CosineSimilarityCalculator.calculate_pose_similarity(
                user_dance.poses[i], standard_dance.poses[i]
            )
            similarities.append(similarity)
        
        # 计算平均相似度
        average_similarity = np.mean(similarities)
        
        # 转换为百分制分数
        score = average_similarity * 100
        grade = DanceScorer._get_grade(average_similarity)
        
        return DanceScore(score, grade, similarities)
    
    @staticmethod
    def _get_grade(similarity: float) -> str:
        if similarity >= DanceScorer.EXCELLENT_THRESHOLD:
            return "优秀"
        elif similarity >= DanceScorer.GOOD_THRESHOLD:
            return "良好"
        elif similarity >= DanceScorer.FAIR_THRESHOLD:
            return "及格"
        else:
            return "需要改进"

class MotionCorrectionSystem:
    """动作纠正建议系统"""
    
    @staticmethod
    def analyze_and_suggest(user_pose: PoseData, standard_pose: PoseData) -> List[str]:
        """分析动作差异并提供建议"""
        suggestions = []
        
        # 关键身体部位索引（基于MediaPipe Pose模型）
        head_points = list(range(0, 11))  # 头部和面部
        arm_points = list(range(11, 23))  # 手臂
        leg_points = list(range(23, 33))  # 腿部
        
        # 分析各部位差异
        head_diff = MotionCorrectionSystem._calculate_part_difference(user_pose, standard_pose, head_points)
        arm_diff = MotionCorrectionSystem._calculate_part_difference(user_pose, standard_pose, arm_points)
        leg_diff = MotionCorrectionSystem._calculate_part_difference(user_pose, standard_pose, leg_points)
        
        # 生成建议
        if head_diff > 0.15:
            suggestions.append("调整头部姿态，保持头部稳定")
        if arm_diff > 0.2:
            suggestions.append("手臂动作需要更加标准，注意手臂的伸展角度")
        if leg_diff > 0.25:
            suggestions.append("腿部动作不够到位，注意步伐的准确性")
        
        if not suggestions:
            suggestions.append("动作很标准，继续保持！")
        
        return suggestions
    
    @staticmethod
    def _calculate_part_difference(user: PoseData, standard: PoseData, points: List[int]) -> float:
        """计算身体部位差异"""
        total_diff = 0.0
        valid_points = 0
        
        for point_index in points:
            if point_index < len(user.landmarks) and point_index < len(standard.landmarks):
                user_point = user.landmarks[point_index]
                standard_point = standard.landmarks[point_index]
                
                # 计算3D欧几里得距离
                diff = sqrt(
                    (user_point.x - standard_point.x) ** 2 +
                    (user_point.y - standard_point.y) ** 2 +
                    (user_point.z - standard_point.z) ** 2
                )
                
                total_diff += diff
                valid_points += 1
        
        return total_diff / valid_points if valid_points > 0 else 0.0

class DanceAnalyzer:
    """舞蹈数据分析器"""
    
    @staticmethod
    def analyze_rhythm(dance: DanceSequence) -> Tuple[float, str]:
        """分析舞蹈节奏"""
        if len(dance.poses) < 2:
            return 0.0, "数据不足"
        
        intervals = []
        for i in range(1, len(dance.poses)):
            interval = dance.poses[i].timestamp - dance.poses[i-1].timestamp
            intervals.append(interval)
        
        # 计算节奏稳定性（标准差）
        mean_interval = np.mean(intervals)
        stability = np.std(intervals)
        
        if stability < 5.0:
            rhythm_quality = "节奏非常稳定"
        elif stability < 10.0:
            rhythm_quality = "节奏较稳定"
        elif stability < 20.0:
            rhythm_quality = "节奏一般"
        else:
            rhythm_quality = "节奏不稳定"
        
        return stability, rhythm_quality
    
    @staticmethod
    def calculate_smoothness(dance: DanceSequence) -> float:
        """计算动作流畅度"""
        if len(dance.poses) < 3:
            return 0.0
        
        total_smoothness = 0.0
        valid_transitions = 0
        
        for i in range(1, len(dance.poses) - 1):
            prev_pose = dance.poses[i-1]
            curr_pose = dance.poses[i]
            next_pose = dance.poses[i+1]
            
            # 计算相邻帧之间的相似度变化
            sim1 = CosineSimilarityCalculator.calculate_pose_similarity(prev_pose, curr_pose)
            sim2 = CosineSimilarityCalculator.calculate_pose_similarity(curr_pose, next_pose)
            
            # 流畅度 = 1 - |相似度变化|
            smoothness = 1.0 - abs(sim1 - sim2)
            total_smoothness += smoothness
            valid_transitions += 1
        
        return total_smoothness / valid_transitions if valid_transitions > 0 else 0.0

# ==================== 模拟数据生成 ====================

class MockDataGenerator:
    """模拟数据生成器"""
    
    @staticmethod
    def generate_random_pose(timestamp: int) -> PoseData:
        """生成模拟姿态数据"""
        pose = PoseData(timestamp)
        
        for i in range(PoseData.POSE_LANDMARKS_COUNT):
            x = random.random()
            y = random.random()
            z = random.random() * 0.5  # z坐标范围较小
            visibility = 0.8 + random.random() * 0.2  # 高可见性
            presence = 0.9 + random.random() * 0.1    # 高存在性
            
            pose.add_landmark(PoseLandmark(x, y, z, visibility, presence, timestamp))
        
        return pose
    
    @staticmethod
    def generate_standard_dance(dance_name: str, frame_count: int) -> DanceSequence:
        """生成标准舞蹈"""
        dance = DanceSequence(dance_name)
        base_time = int(time.time() * 1000)
        
        for i in range(frame_count):
            timestamp = base_time + i * 33  # 30fps
            dance.add_pose(MockDataGenerator.generate_random_pose(timestamp))
        
        return dance
    
    @staticmethod
    def generate_user_dance(standard: DanceSequence, similarity: float) -> DanceSequence:
        """生成用户舞蹈数据"""
        user_dance = DanceSequence("用户表演")
        
        for standard_pose in standard.poses:
            user_pose = PoseData(standard_pose.timestamp)
            
            for standard_landmark in standard_pose.landmarks:
                # 根据相似度添加噪声
                noise = (1.0 - similarity) * 0.1
                x = standard_landmark.x + (random.random() - 0.5) * noise
                y = standard_landmark.y + (random.random() - 0.5) * noise
                z = standard_landmark.z + (random.random() - 0.5) * noise
                
                user_pose.add_landmark(PoseLandmark(
                    x, y, z,
                    standard_landmark.visibility,
                    standard_landmark.presence,
                    standard_landmark.timestamp
                ))
            
            user_dance.add_pose(user_pose)
        
        return user_dance

# ==================== 辅助函数 ====================

def generate_progress_bar(percentage: float, length: int = 20) -> str:
    """生成进度条"""
    filled = int(percentage * length)
    bar = "[" + "█" * filled + "░" * (length - filled) + "]"
    return bar

def analyze_feedback(feedback: List[str]) -> str:
    """分析反馈统计"""
    counts = {}
    for f in feedback:
        counts[f] = counts.get(f, 0) + 1

    result = []
    for key, value in counts.items():
        result.append(f"{key}×{value}")

    return ", ".join(result)

def simulate_realtime_scoring(standard_dance: DanceSequence):
    """模拟实时评分过程"""
    print("   模拟实时舞蹈评分中...")

    total_score = 0.0
    realtime_feedback = []

    for i in range(min(10, len(standard_dance.poses))):
        # 模拟用户当前姿态
        current_user_pose = MockDataGenerator.generate_random_pose(int(time.time() * 1000))
        standard_pose = standard_dance.poses[i]

        # 计算实时相似度
        similarity = CosineSimilarityCalculator.calculate_pose_similarity(
            current_user_pose, standard_pose)

        total_score += similarity

        # 显示进度条和分数
        progress_bar = generate_progress_bar(similarity, 20)
        print(f"   帧 {i+1:2d}: {progress_bar} {similarity*100:.1f}%", end="")

        # 实时反馈
        if similarity > 0.9:
            print(" 🌟 完美!")
            realtime_feedback.append("完美")
        elif similarity > 0.75:
            print(" ✅ 很好!")
            realtime_feedback.append("很好")
        elif similarity > 0.6:
            print(" ⚠️  需要调整")
            realtime_feedback.append("需要调整")
        else:
            print(" ❌ 动作不准确")
            realtime_feedback.append("不准确")

        # 模拟处理延迟
        time.sleep(0.15)

    # 显示实时评分总结
    avg_score = total_score / min(10, len(standard_dance.poses))
    print(f"   实时评分平均值: {avg_score*100:.1f}% | 反馈统计: {analyze_feedback(realtime_feedback)}")

def demonstrate_detailed_analysis(standard_dance: DanceSequence):
    """详细分析演示"""
    # 生成一个中等水平的用户表演
    user_dance = MockDataGenerator.generate_user_dance(standard_dance, 0.75)

    print("   分析用户舞蹈表演的各个维度:")

    # 1. 整体评分
    overall_score = DanceScorer.evaluate_performance(user_dance, standard_dance)
    print(f"   • 整体评分: {overall_score}")

    # 2. 节奏分析
    stability, rhythm_quality = DanceAnalyzer.analyze_rhythm(user_dance)
    print(f"   • 节奏分析: 节奏稳定性: {stability:.2f} | {rhythm_quality}")

    # 3. 流畅度分析
    smoothness = DanceAnalyzer.calculate_smoothness(user_dance)
    print(f"   • 动作流畅度: {smoothness*100:.1f}%")

    # 4. 关键帧分析
    print("   • 关键帧分析:")
    key_frames = [0, len(user_dance.poses)//4, len(user_dance.poses)//2,
                  3*len(user_dance.poses)//4, len(user_dance.poses)-1]

    for frame_index in key_frames:
        if frame_index < len(user_dance.poses) and frame_index < len(standard_dance.poses):
            frame_similarity = CosineSimilarityCalculator.calculate_pose_similarity(
                user_dance.poses[frame_index], standard_dance.poses[frame_index])

            suggestions = MotionCorrectionSystem.analyze_and_suggest(
                user_dance.poses[frame_index], standard_dance.poses[frame_index])

            print(f"     帧 {frame_index+1}: {frame_similarity*100:.1f}% - {suggestions[0]}")

    # 5. 改进建议总结
    print("   • 综合改进建议:")
    if overall_score.score >= 85:
        print("     - 表现优秀，继续保持当前水平")
        print("     - 可以尝试更复杂的舞蹈动作")
    elif overall_score.score >= 70:
        print("     - 基础动作掌握良好，需要提高精确度")
        print("     - 注意动作的细节和表现力")
    elif overall_score.score >= 60:
        print("     - 需要加强基础动作练习")
        print("     - 建议跟随慢速教学视频练习")
    else:
        print("     - 建议从基础动作开始学习")
        print("     - 多观看标准动作示范")

    # 6. 技术指标总结
    print("   • 技术指标:")
    print("     - 姿态检测精度: 33个关键点")
    print("     - 处理帧率: 30 FPS")
    print("     - 相似度算法: 余弦相似度")
    print("     - 分析维度: 4个（整体、节奏、流畅度、关键帧）")

# ==================== 主程序 ====================

def main():
    print("=== AI跳舞功能完整演示系统 ===\n")

    # 1. 生成标准舞蹈动作
    print("1. 加载标准舞蹈动作...")
    standard_dance = MockDataGenerator.generate_standard_dance("经典舞蹈", 30)
    print(f"   标准舞蹈包含 {len(standard_dance.poses)} 帧动作，持续时间: {standard_dance.duration} ms\n")

    # 2. 模拟不同水平的用户表演
    performance_levels = [0.95, 0.85, 0.70, 0.50]
    level_names = ["专业级", "熟练级", "初学级", "入门级"]

    print("2. 评估不同水平的舞蹈表演:")
    print("   " + "=" * 70)

    for i, level in enumerate(performance_levels):
        # 生成用户舞蹈数据
        user_dance = MockDataGenerator.generate_user_dance(standard_dance, level)

        # 基础评分
        score = DanceScorer.evaluate_performance(user_dance, standard_dance)
        print(f"   {level_names[i]}表演: {score}")

        # 节奏分析
        stability, rhythm_quality = DanceAnalyzer.analyze_rhythm(user_dance)
        print(f"   节奏稳定性: {stability:.2f} | {rhythm_quality}")

        # 流畅度分析
        smoothness = DanceAnalyzer.calculate_smoothness(user_dance)
        print(f"   动作流畅度: {smoothness*100:.1f}%")

        # 动作建议（仅显示第一帧的建议）
        if user_dance.poses and standard_dance.poses:
            suggestions = MotionCorrectionSystem.analyze_and_suggest(
                user_dance.poses[0], standard_dance.poses[0])
            print(f"   改进建议: {suggestions[0]}")

        print()

    # 3. 实时评分演示
    print("3. 实时评分演示:")
    print("   " + "=" * 70)
    simulate_realtime_scoring(standard_dance)

    # 4. 详细分析演示
    print("\n4. 详细舞蹈分析:")
    print("   " + "=" * 70)
    demonstrate_detailed_analysis(standard_dance)

    print("\n=== 演示完成 ===")
    print("\n核心技术总结:")
    print("• MediaPipe姿态检测 - 33个关键点实时追踪")
    print("• 余弦相似度算法 - 动作相似度计算")
    print("• 时间序列分析 - 节奏和流畅度评估")
    print("• 智能纠错系统 - 个性化改进建议")

if __name__ == "__main__":
    main()
