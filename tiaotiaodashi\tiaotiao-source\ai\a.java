package ai;

import android.os.Build;
import android.security.NetworkSecurityPolicy;
import bi.c;
import bi.d;
import bi.h;
import bi.i;
import bi.k;
import bi.l;
import bi.m;
import di.c;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.X509TrustManager;
import lg.l;
import rh.x;
import xg.g;
import xg.m;

public final class a extends j {
  static {
    if (j.a.h() && Build.VERSION.SDK_INT >= 29) {
      bool = true;
    } else {
      bool = false;
    } 
    f = bool;
  }
  
  public a() {
    List list = l.l((Object[])new m[] { c.a.a(), (m)new l(h.f.d()), (m)new l(k.a.a()), (m)new l(i.a.a()) });
    ArrayList<m> arrayList = new ArrayList();
    for (m m : list) {
      if (((m)m).b())
        arrayList.add(m); 
    } 
    this.d = arrayList;
  }
  
  public c c(X509TrustManager paramX509TrustManager) {
    d d1;
    c c;
    m.e(paramX509TrustManager, "trustManager");
    d d2 = d.d.a(paramX509TrustManager);
    if (d2 != null) {
      d1 = d2;
    } else {
      c = super.c((X509TrustManager)d1);
    } 
    return c;
  }
  
  public void e(SSLSocket paramSSLSocket, String paramString, List<? extends x> paramList) {
    // Byte code:
    //   0: aload_1
    //   1: ldc 'sslSocket'
    //   3: invokestatic e : (Ljava/lang/Object;Ljava/lang/String;)V
    //   6: aload_3
    //   7: ldc 'protocols'
    //   9: invokestatic e : (Ljava/lang/Object;Ljava/lang/String;)V
    //   12: aload_0
    //   13: getfield d : Ljava/util/List;
    //   16: checkcast java/lang/Iterable
    //   19: invokeinterface iterator : ()Ljava/util/Iterator;
    //   24: astore #5
    //   26: aload #5
    //   28: invokeinterface hasNext : ()Z
    //   33: ifeq -> 62
    //   36: aload #5
    //   38: invokeinterface next : ()Ljava/lang/Object;
    //   43: astore #4
    //   45: aload #4
    //   47: checkcast bi/m
    //   50: aload_1
    //   51: invokeinterface a : (Ljavax/net/ssl/SSLSocket;)Z
    //   56: ifeq -> 26
    //   59: goto -> 65
    //   62: aconst_null
    //   63: astore #4
    //   65: aload #4
    //   67: checkcast bi/m
    //   70: astore #4
    //   72: aload #4
    //   74: ifnull -> 87
    //   77: aload #4
    //   79: aload_1
    //   80: aload_2
    //   81: aload_3
    //   82: invokeinterface d : (Ljavax/net/ssl/SSLSocket;Ljava/lang/String;Ljava/util/List;)V
    //   87: return
  }
  
  public String g(SSLSocket paramSSLSocket) {
    String str;
    m m2;
    m.e(paramSSLSocket, "sslSocket");
    Iterator<m> iterator = this.d.iterator();
    while (true) {
      boolean bool = iterator.hasNext();
      m2 = null;
      if (bool) {
        m m = (m)iterator.next();
        if (((m)m).a(paramSSLSocket))
          break; 
        continue;
      } 
      m1 = null;
      break;
    } 
    m m3 = m1;
    m m1 = m2;
    if (m3 != null)
      str = m3.c(paramSSLSocket); 
    return str;
  }
  
  public boolean i(String paramString) {
    m.e(paramString, "hostname");
    return NetworkSecurityPolicy.getInstance().isCleartextTrafficPermitted(paramString);
  }
  
  static {
    boolean bool;
  }
  
  public static final a e = new a(null);
  
  public static final boolean f;
  
  public final List<m> d;
  
  public static final class a {
    public a() {}
    
    public final j a() {
      j j;
      if (b()) {
        j = new a();
      } else {
        j = null;
      } 
      return j;
    }
    
    public final boolean b() {
      return a.p();
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ai\a.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */