package ai;

import gh.m;
import java.util.List;
import javax.net.ssl.SSLParameters;
import javax.net.ssl.SSLSocket;
import rh.x;
import xg.g;
import xg.m;

public class h extends j {
  public static final a d = new a(null);
  
  public static final boolean e;
  
  static {
    String str = System.getProperty("java.specification.version");
    if (str != null)
      integer = m.i(str); 
    boolean bool = true;
    if (integer != null) {
      if (integer.intValue() >= 9) {
        e = bool;
        return;
      } 
    } else {
      try {
        SSLSocket.class.getMethod("getApplicationProtocol", new Class[0]);
        e = bool;
        return;
      } catch (NoSuchMethodException noSuchMethodException) {}
    } 
    bool = false;
    e = bool;
  }
  
  public void e(SSLSocket paramSSLSocket, String paramString, List<x> paramList) {
    m.e(paramSSLSocket, "sslSocket");
    m.e(paramList, "protocols");
    SSLParameters sSLParameters = paramSSLSocket.getSSLParameters();
    f.a(sSLParameters, j.a.b(paramList).<String>toArray(new String[0]));
    paramSSLSocket.setSSLParameters(sSLParameters);
  }
  
  public String g(SSLSocket paramSSLSocket) {
    String str1;
    m.e(paramSSLSocket, "sslSocket");
    String str2 = null;
    try {
      boolean bool;
      str1 = g.a(paramSSLSocket);
      if (str1 == null) {
        bool = true;
      } else {
        bool = m.a(str1, "");
      } 
      if (bool)
        str1 = str2; 
    } catch (UnsupportedOperationException unsupportedOperationException) {
      str1 = str2;
    } 
    return str1;
  }
  
  static {
    Integer integer = null;
  }
  
  public static final class a {
    public a() {}
    
    public final h a() {
      h h;
      if (b()) {
        h = new h();
      } else {
        h = null;
      } 
      return h;
    }
    
    public final boolean b() {
      return h.p();
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ai\h.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */