package ai;

import java.security.KeyStore;
import java.security.Provider;
import java.util.Arrays;
import java.util.List;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLParameters;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import org.openjsse.javax.net.ssl.SSLParameters;
import org.openjsse.javax.net.ssl.SSLSocket;
import org.openjsse.net.ssl.OpenJSSE;
import rh.x;
import xg.g;
import xg.m;

public final class i extends j {
  public static final a e;
  
  public static final boolean f;
  
  public final Provider d = (Provider)new OpenJSSE();
  
  static {
    a a1 = new a(null);
    e = a1;
    boolean bool = false;
    try {
      Class.forName("org.openjsse.net.ssl.OpenJSSE", false, a1.getClass().getClassLoader());
      bool = true;
    } catch (ClassNotFoundException classNotFoundException) {}
    f = bool;
  }
  
  public i() {}
  
  public void e(SSLSocket paramSSLSocket, String paramString, List<x> paramList) {
    SSLSocket sSLSocket;
    SSLParameters sSLParameters;
    m.e(paramSSLSocket, "sslSocket");
    m.e(paramList, "protocols");
    if (paramSSLSocket instanceof SSLSocket) {
      sSLSocket = (SSLSocket)paramSSLSocket;
      sSLParameters = sSLSocket.getSSLParameters();
      if (sSLParameters instanceof SSLParameters) {
        paramList = (List)j.a.b(paramList);
        ((SSLParameters)sSLParameters).setApplicationProtocols(paramList.<String>toArray(new String[0]));
        sSLSocket.setSSLParameters(sSLParameters);
      } 
    } else {
      super.e((SSLSocket)sSLSocket, (String)sSLParameters, paramList);
    } 
  }
  
  public String g(SSLSocket paramSSLSocket) {
    String str;
    m.e(paramSSLSocket, "sslSocket");
    if (paramSSLSocket instanceof SSLSocket) {
      boolean bool;
      str = ((SSLSocket)paramSSLSocket).getApplicationProtocol();
      if (str == null) {
        bool = true;
      } else {
        bool = m.a(str, "");
      } 
      if (bool)
        str = null; 
    } else {
      str = super.g((SSLSocket)str);
    } 
    return str;
  }
  
  public SSLContext m() {
    SSLContext sSLContext = SSLContext.getInstance("TLSv1.3", this.d);
    m.d(sSLContext, "getInstance(\"TLSv1.3\", provider)");
    return sSLContext;
  }
  
  public X509TrustManager o() {
    TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm(), this.d);
    trustManagerFactory.init((KeyStore)null);
    TrustManager[] arrayOfTrustManager = trustManagerFactory.getTrustManagers();
    m.b(arrayOfTrustManager);
    int k = arrayOfTrustManager.length;
    boolean bool = true;
    if (k != 1 || !(arrayOfTrustManager[0] instanceof X509TrustManager))
      bool = false; 
    if (bool) {
      TrustManager trustManager = arrayOfTrustManager[0];
      m.c(trustManager, "null cannot be cast to non-null type javax.net.ssl.X509TrustManager");
      return (X509TrustManager)trustManager;
    } 
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("Unexpected default trust managers: ");
    String str = Arrays.toString((Object[])arrayOfTrustManager);
    m.d(str, "toString(this)");
    stringBuilder.append(str);
    throw new IllegalStateException(stringBuilder.toString().toString());
  }
  
  public static final class a {
    public a() {}
    
    public final i a() {
      boolean bool = b();
      i i = null;
      if (bool)
        i = new i(null); 
      return i;
    }
    
    public final boolean b() {
      return i.p();
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ai\i.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */