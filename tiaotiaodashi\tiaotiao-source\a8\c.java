package a8;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import d8.a;
import d8.e;
import java.util.ArrayList;
import java.util.List;
import xg.m;

public final class c {
  public Activity a;
  
  public Application b;
  
  public boolean c;
  
  public final a d = a.b.a();
  
  public final List<String> e = new ArrayList<String>();
  
  public final List<String> f = new ArrayList<String>();
  
  public final List<String> g = new ArrayList<String>();
  
  public b h;
  
  public final c a(int paramInt, String[] paramArrayOfString, int[] paramArrayOfint) {
    m.e(paramArrayOfString, "permissions");
    m.e(paramArrayOfint, "grantResults");
    if (paramInt == 3001 || paramInt == 3002) {
      int i = paramArrayOfString.length;
      for (byte b1 = 0; b1 < i; b1++) {
        List<String> list1;
        String str = paramArrayOfString[b1];
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("Returned permissions: ");
        stringBuilder.append(str);
        a.d(stringBuilder.toString());
        int j = paramArrayOfint[b1];
        if (j == -1) {
          list1 = this.f;
          str = paramArrayOfString[b1];
        } else if (j == 0) {
          list1 = this.g;
          str = paramArrayOfString[b1];
        } else {
          continue;
        } 
        list1.add(str);
        continue;
      } 
      a.a("dealResult: ");
      StringBuilder stringBuilder1 = new StringBuilder();
      stringBuilder1.append("  permissions: ");
      stringBuilder1.append(paramArrayOfString);
      a.a(stringBuilder1.toString());
      stringBuilder1 = new StringBuilder();
      stringBuilder1.append("  grantResults: ");
      stringBuilder1.append(paramArrayOfint);
      a.a(stringBuilder1.toString());
      List<String> list = this.f;
      StringBuilder stringBuilder2 = new StringBuilder();
      stringBuilder2.append("  deniedPermissionsList: ");
      stringBuilder2.append(list);
      a.a(stringBuilder2.toString());
      list = this.g;
      stringBuilder2 = new StringBuilder();
      stringBuilder2.append("  grantedPermissionsList: ");
      stringBuilder2.append(list);
      a.a(stringBuilder2.toString());
      if (this.d.k()) {
        a a1 = this.d;
        Application application = this.b;
        m.b(application);
        a1.d(this, (Context)application, paramArrayOfString, paramArrayOfint, this.e, this.f, this.g, paramInt);
      } else if ((this.f.isEmpty() ^ true) != 0) {
        b b2 = this.h;
        m.b(b2);
        b2.b(this.f, this.g, this.e);
      } else {
        b b2 = this.h;
        m.b(b2);
        b2.a(this.e);
      } 
    } 
    i();
    this.c = false;
    return this;
  }
  
  public final Activity b() {
    return this.a;
  }
  
  public final void c(Context paramContext) {
    Intent intent = new Intent();
    intent.addFlags(268435456);
    intent.addFlags(1073741824);
    intent.addFlags(8388608);
    intent.addCategory("android.intent.category.DEFAULT");
    intent.setAction("android.settings.APPLICATION_DETAILS_SETTINGS");
    m.b(paramContext);
    intent.setData(Uri.fromParts("package", paramContext.getPackageName(), null));
    paramContext.startActivity(intent);
  }
  
  public final x7.c d(int paramInt, boolean paramBoolean) {
    a a1 = this.d;
    Application application = this.b;
    m.b(application);
    return a1.a(application, paramInt, paramBoolean);
  }
  
  public final b e() {
    return this.h;
  }
  
  public final boolean f(Context paramContext) {
    m.e(paramContext, "applicationContext");
    return this.d.f(paramContext);
  }
  
  public final void g(int paramInt, e parame) {
    m.e(parame, "resultHandler");
    a a1 = this.d;
    Application application = this.b;
    m.b(application);
    a1.l(this, application, paramInt, parame);
  }
  
  public final c h(Context paramContext, int paramInt, boolean paramBoolean) {
    m.e(paramContext, "applicationContext");
    this.d.m(this, paramContext, paramInt, paramBoolean);
    return this;
  }
  
  public final void i() {
    if ((this.f.isEmpty() ^ true) != 0)
      this.f.clear(); 
    if ((this.e.isEmpty() ^ true) != 0)
      this.e.clear(); 
  }
  
  public final c j(b paramb) {
    this.h = paramb;
    return this;
  }
  
  public final void k(List<String> paramList) {
    m.e(paramList, "permission");
    this.e.clear();
    this.e.addAll(paramList);
  }
  
  public final void l(b paramb) {
    this.h = paramb;
  }
  
  public final c m(Activity paramActivity) {
    this.a = paramActivity;
    if (paramActivity != null) {
      Application application = paramActivity.getApplication();
    } else {
      paramActivity = null;
    } 
    this.b = (Application)paramActivity;
    return this;
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a8\c.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */