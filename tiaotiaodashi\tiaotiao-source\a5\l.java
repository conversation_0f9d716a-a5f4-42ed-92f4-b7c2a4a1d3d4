package a5;

import java.util.Arrays;

public class l {
  public static final byte[] a = new byte[] { 48, 49, 53, 0 };
  
  public static final byte[] b = new byte[] { 48, 49, 48, 0 };
  
  public static final byte[] c = new byte[] { 48, 48, 57, 0 };
  
  public static final byte[] d = new byte[] { 48, 48, 53, 0 };
  
  public static final byte[] e = new byte[] { 48, 48, 49, 0 };
  
  public static final byte[] f = new byte[] { 48, 48, 49, 0 };
  
  public static final byte[] g = new byte[] { 48, 48, 50, 0 };
  
  public static String a(byte[] paramArrayOfbyte) {
    return Arrays.equals(paramArrayOfbyte, e) ? ":" : (Arrays.equals(paramArrayOfbyte, d) ? ":" : "!");
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a5\l.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */