package a8;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import b8.b;
import b8.c;
import b8.d;
import d8.e;
import java.util.List;
import lg.i;
import w0.b;
import x7.c;
import xg.g;
import xg.m;

public abstract class a {
  public static final a b = new a(null);
  
  public e a;
  
  public abstract c a(Application paramApplication, int paramInt, boolean paramBoolean);
  
  public final e b() {
    return this.a;
  }
  
  public final String c() {
    String str = getClass().getSimpleName();
    m.d(str, "getSimpleName(...)");
    return str;
  }
  
  public void d(c paramc, Context paramContext, String[] paramArrayOfString, int[] paramArrayOfint, List<String> paramList1, List<String> paramList2, List<String> paramList3, int paramInt) {
    m.e(paramc, "permissionsUtils");
    m.e(paramContext, "context");
    m.e(paramArrayOfString, "permissions");
    m.e(paramArrayOfint, "grantResults");
    m.e(paramList1, "needToRequestPermissionsList");
    m.e(paramList2, "deniedPermissionsList");
    m.e(paramList3, "grantedPermissionsList");
    throw new UnsupportedOperationException("handlePermissionResult is not implemented, please implement it in your delegate.");
  }
  
  public final boolean e(Context paramContext, String... paramVarArgs) {
    boolean bool1;
    m.e(paramContext, "context");
    m.e(paramVarArgs, "permissions");
    int i = paramVarArgs.length;
    boolean bool2 = false;
    byte b = 0;
    while (true) {
      bool1 = bool2;
      if (b < i) {
        if (h(paramContext, paramVarArgs[b])) {
          bool1 = true;
          break;
        } 
        b++;
        continue;
      } 
      break;
    } 
    return bool1;
  }
  
  public abstract boolean f(Context paramContext);
  
  public final boolean g(Context paramContext, String paramString) {
    boolean bool;
    m.e(paramContext, "context");
    m.e(paramString, "permission");
    if (i(paramContext, paramString) && h(paramContext, paramString)) {
      bool = true;
    } else {
      bool = false;
    } 
    return bool;
  }
  
  public final boolean h(Context paramContext, String paramString) {
    boolean bool;
    m.e(paramContext, "context");
    m.e(paramString, "permission");
    if (x0.a.a(paramContext, paramString) == 0) {
      bool = true;
    } else {
      bool = false;
    } 
    return bool;
  }
  
  public final boolean i(Context paramContext, String paramString) {
    PackageInfo packageInfo;
    m.e(paramContext, "context");
    m.e(paramString, "permission");
    ApplicationInfo applicationInfo = paramContext.getApplicationInfo();
    int i = Build.VERSION.SDK_INT;
    PackageManager packageManager = paramContext.getPackageManager();
    String str = applicationInfo.packageName;
    if (i >= 33) {
      packageInfo = packageManager.getPackageInfo(str, PackageManager.PackageInfoFlags.of(4096L));
    } else {
      packageInfo = packageInfo.getPackageInfo(str, 4096);
    } 
    String[] arrayOfString = packageInfo.requestedPermissions;
    boolean bool2 = false;
    boolean bool1 = bool2;
    if (arrayOfString != null) {
      bool1 = bool2;
      if (i.o((Object[])arrayOfString, paramString) == true)
        bool1 = true; 
    } 
    return bool1;
  }
  
  public final boolean j(Context paramContext, String... paramVarArgs) {
    // Byte code:
    //   0: aload_1
    //   1: ldc 'context'
    //   3: invokestatic e : (Ljava/lang/Object;Ljava/lang/String;)V
    //   6: aload_2
    //   7: ldc 'permission'
    //   9: invokestatic e : (Ljava/lang/Object;Ljava/lang/String;)V
    //   12: aload_2
    //   13: arraylength
    //   14: istore #4
    //   16: iconst_0
    //   17: istore #5
    //   19: iconst_0
    //   20: istore_3
    //   21: iload_3
    //   22: iload #4
    //   24: if_icmpge -> 47
    //   27: aload_0
    //   28: aload_1
    //   29: aload_2
    //   30: iload_3
    //   31: aaload
    //   32: invokevirtual g : (Landroid/content/Context;Ljava/lang/String;)Z
    //   35: ifne -> 41
    //   38: goto -> 50
    //   41: iinc #3, 1
    //   44: goto -> 21
    //   47: iconst_1
    //   48: istore #5
    //   50: aload_0
    //   51: invokevirtual c : ()Ljava/lang/String;
    //   54: astore_1
    //   55: aload_2
    //   56: invokestatic K : ([Ljava/lang/Object;)Ljava/util/List;
    //   59: astore_2
    //   60: new java/lang/StringBuilder
    //   63: dup
    //   64: invokespecial <init> : ()V
    //   67: astore #6
    //   69: aload #6
    //   71: ldc '['
    //   73: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   76: pop
    //   77: aload #6
    //   79: aload_1
    //   80: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   83: pop
    //   84: aload #6
    //   86: ldc '] havePermissions: '
    //   88: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   91: pop
    //   92: aload #6
    //   94: aload_2
    //   95: invokevirtual append : (Ljava/lang/Object;)Ljava/lang/StringBuilder;
    //   98: pop
    //   99: aload #6
    //   101: ldc ', result: '
    //   103: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   106: pop
    //   107: aload #6
    //   109: iload #5
    //   111: invokevirtual append : (Z)Ljava/lang/StringBuilder;
    //   114: pop
    //   115: aload #6
    //   117: invokevirtual toString : ()Ljava/lang/String;
    //   120: invokestatic a : (Ljava/lang/Object;)V
    //   123: iload #5
    //   125: ireturn
  }
  
  public boolean k() {
    return false;
  }
  
  public void l(c paramc, Application paramApplication, int paramInt, e parame) {
    m.e(paramc, "permissionsUtils");
    m.e(paramApplication, "context");
    m.e(parame, "resultHandler");
    String str = c();
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("[");
    stringBuilder.append(str);
    stringBuilder.append("] presentLimited is not implemented");
    d8.a.a(stringBuilder.toString());
    parame.g(null);
  }
  
  public abstract void m(c paramc, Context paramContext, int paramInt, boolean paramBoolean);
  
  public final void n(c paramc, List<String> paramList, int paramInt) {
    m.e(paramc, "permissionsUtils");
    m.e(paramList, "permission");
    Activity activity = paramc.b();
    if (activity != null) {
      paramc.k(paramList);
      b.r(activity, paramList.<String>toArray(new String[0]), paramInt);
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("requestPermission: ");
      stringBuilder.append(paramList);
      stringBuilder.append(" for code ");
      stringBuilder.append(paramInt);
      d8.a.a(stringBuilder.toString());
      return;
    } 
    throw new NullPointerException("Activity for the permission request is not exist.");
  }
  
  public final void p(e parame) {
    this.a = parame;
  }
  
  public static final class a {
    public a() {}
    
    public final a a() {
      boolean bool1;
      int i = Build.VERSION.SDK_INT;
      boolean bool2 = true;
      if (i < 29) {
        bool1 = true;
      } else {
        bool1 = false;
      } 
      if (bool1) {
        b8.a a1 = new b8.a();
      } else {
        if (29 <= i && i < 33) {
          bool1 = true;
        } else {
          bool1 = false;
        } 
        if (bool1) {
          b b = new b();
        } else if (i == 33) {
          c c = new c();
        } else {
          if (34 <= i && i < Integer.MAX_VALUE) {
            bool1 = bool2;
          } else {
            bool1 = false;
          } 
          if (bool1)
            return (a)new d(); 
          throw new UnsupportedOperationException("This sdk version is not supported yet.");
        } 
      } 
      return (a)SYNTHETIC_LOCAL_VARIABLE_4;
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a8\a.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */