package ad;

import eb.p;

public class a extends Exception {
  public final int a;
  
  public a(String paramString, int paramInt) {
    super(p.f(paramString, "Provided message must not be empty."));
    this.a = paramInt;
  }
  
  public a(String paramString, int paramInt, Throwable paramThrowable) {
    super(p.f(paramString, "Provided message must not be empty."), paramThrowable);
    this.a = paramInt;
  }
  
  public int a() {
    return this.a;
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ad\a.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */