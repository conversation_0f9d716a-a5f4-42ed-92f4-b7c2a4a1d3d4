package android.support.v4.media.session;

import android.app.PendingIntent;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.media.MediaDescription;
import android.media.MediaMetadata;
import android.media.Rating;
import android.media.session.MediaSession;
import android.media.session.PlaybackState;
import android.net.Uri;
import android.os.BadParcelableException;
import android.os.Binder;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.Parcel;
import android.os.Parcelable;
import android.os.RemoteCallbackList;
import android.os.RemoteException;
import android.os.ResultReceiver;
import android.support.v4.media.MediaDescriptionCompat;
import android.support.v4.media.MediaMetadataCompat;
import android.support.v4.media.RatingCompat;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import java.lang.ref.WeakReference;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class MediaSessionCompat {
  public static final int d;
  
  public static int e;
  
  public final c a;
  
  public final MediaControllerCompat b;
  
  public final ArrayList<h> c = new ArrayList<h>();
  
  static {
    boolean bool;
    if (f1.a.b()) {
      bool = true;
    } else {
      bool = false;
    } 
    d = bool;
  }
  
  public MediaSessionCompat(Context paramContext, String paramString, ComponentName paramComponentName, PendingIntent paramPendingIntent) {
    this(paramContext, paramString, paramComponentName, paramPendingIntent, null);
  }
  
  public MediaSessionCompat(Context paramContext, String paramString, ComponentName paramComponentName, PendingIntent paramPendingIntent, Bundle paramBundle) {
    this(paramContext, paramString, paramComponentName, paramPendingIntent, paramBundle, null);
  }
  
  public MediaSessionCompat(Context paramContext, String paramString, ComponentName paramComponentName, PendingIntent paramPendingIntent, Bundle paramBundle, m5.d paramd) {
    if (paramContext != null) {
      if (!TextUtils.isEmpty(paramString)) {
        e e;
        Looper looper;
        ComponentName componentName = paramComponentName;
        if (paramComponentName == null) {
          paramComponentName = k2.a.a(paramContext);
          componentName = paramComponentName;
          if (paramComponentName == null) {
            Log.w("MediaSessionCompat", "Couldn't find a unique registered media button receiver in the given context.");
            componentName = paramComponentName;
          } 
        } 
        PendingIntent pendingIntent = paramPendingIntent;
        if (componentName != null) {
          pendingIntent = paramPendingIntent;
          if (paramPendingIntent == null) {
            Intent intent = new Intent("android.intent.action.MEDIA_BUTTON");
            intent.setComponent(componentName);
            pendingIntent = PendingIntent.getBroadcast(paramContext, 0, intent, d);
          } 
        } 
        int i = Build.VERSION.SDK_INT;
        if (i >= 29) {
          e = new g(paramContext, paramString, paramd, paramBundle);
        } else {
          f f;
          if (i >= 28) {
            f = new f(paramContext, (String)e, paramd, paramBundle);
          } else {
            e = new e(paramContext, (String)f, paramd, paramBundle);
          } 
        } 
        this.a = e;
        if (Looper.myLooper() != null) {
          looper = Looper.myLooper();
        } else {
          looper = Looper.getMainLooper();
        } 
        Handler handler = new Handler(looper);
        h(new a(this), handler);
        this.a.h(pendingIntent);
        this.b = new MediaControllerCompat(paramContext, this);
        if (e == 0)
          e = (int)(TypedValue.applyDimension(1, 320.0F, paramContext.getResources().getDisplayMetrics()) + 0.5F); 
        return;
      } 
      throw new IllegalArgumentException("tag must not be null or empty");
    } 
    throw new IllegalArgumentException("context must not be null");
  }
  
  public static void a(Bundle paramBundle) {
    if (paramBundle != null)
      paramBundle.setClassLoader(MediaSessionCompat.class.getClassLoader()); 
  }
  
  public static PlaybackStateCompat d(PlaybackStateCompat paramPlaybackStateCompat, MediaMetadataCompat paramMediaMetadataCompat) {
    PlaybackStateCompat playbackStateCompat = paramPlaybackStateCompat;
    if (paramPlaybackStateCompat != null) {
      long l1 = paramPlaybackStateCompat.k();
      long l2 = -1L;
      if (l1 == -1L) {
        playbackStateCompat = paramPlaybackStateCompat;
      } else {
        if (paramPlaybackStateCompat.l() != 3 && paramPlaybackStateCompat.l() != 4) {
          playbackStateCompat = paramPlaybackStateCompat;
          if (paramPlaybackStateCompat.l() == 5) {
            l1 = paramPlaybackStateCompat.h();
            playbackStateCompat = paramPlaybackStateCompat;
          } 
          return playbackStateCompat;
        } 
        l1 = paramPlaybackStateCompat.h();
        playbackStateCompat = paramPlaybackStateCompat;
      } 
    } 
    return playbackStateCompat;
  }
  
  public static Bundle n(Bundle paramBundle) {
    if (paramBundle == null)
      return null; 
    a(paramBundle);
    try {
      paramBundle.isEmpty();
      return paramBundle;
    } catch (BadParcelableException badParcelableException) {
      Log.e("MediaSessionCompat", "Could not unparcel the data.");
      return null;
    } 
  }
  
  public MediaControllerCompat b() {
    return this.b;
  }
  
  public Token c() {
    return this.a.b();
  }
  
  public void e() {
    this.a.release();
  }
  
  public void f(boolean paramBoolean) {
    this.a.i(paramBoolean);
    Iterator<h> iterator = this.c.iterator();
    while (iterator.hasNext())
      ((h)iterator.next()).a(); 
  }
  
  public void g(b paramb) {
    h(paramb, null);
  }
  
  public void h(b paramb, Handler paramHandler) {
    if (paramb == null) {
      this.a.d(null, null);
    } else {
      c c1 = this.a;
      if (paramHandler == null)
        paramHandler = new Handler(); 
      c1.d(paramb, paramHandler);
    } 
  }
  
  public void i(int paramInt) {
    this.a.a(paramInt);
  }
  
  public void j(MediaMetadataCompat paramMediaMetadataCompat) {
    this.a.f(paramMediaMetadataCompat);
  }
  
  public void k(PlaybackStateCompat paramPlaybackStateCompat) {
    this.a.k(paramPlaybackStateCompat);
  }
  
  public void l(int paramInt) {
    this.a.j(paramInt);
  }
  
  public void m(int paramInt) {
    this.a.r(paramInt);
  }
  
  public static final class QueueItem implements Parcelable {
    public static final Parcelable.Creator<QueueItem> CREATOR = new a();
    
    public final MediaDescriptionCompat a;
    
    public final long b;
    
    public MediaSession.QueueItem c;
    
    public QueueItem(MediaSession.QueueItem param1QueueItem, MediaDescriptionCompat param1MediaDescriptionCompat, long param1Long) {
      if (param1MediaDescriptionCompat != null) {
        if (param1Long != -1L) {
          this.a = param1MediaDescriptionCompat;
          this.b = param1Long;
          this.c = param1QueueItem;
          return;
        } 
        throw new IllegalArgumentException("Id cannot be QueueItem.UNKNOWN_ID");
      } 
      throw new IllegalArgumentException("Description cannot be null");
    }
    
    public QueueItem(Parcel param1Parcel) {
      this.a = (MediaDescriptionCompat)MediaDescriptionCompat.CREATOR.createFromParcel(param1Parcel);
      this.b = param1Parcel.readLong();
    }
    
    public static QueueItem c(Object param1Object) {
      if (param1Object != null) {
        param1Object = param1Object;
        return new QueueItem((MediaSession.QueueItem)param1Object, MediaDescriptionCompat.c(b.b((MediaSession.QueueItem)param1Object)), b.c((MediaSession.QueueItem)param1Object));
      } 
      return null;
    }
    
    public static List<QueueItem> d(List<?> param1List) {
      if (param1List != null) {
        ArrayList<QueueItem> arrayList = new ArrayList();
        Iterator<?> iterator = param1List.iterator();
        while (iterator.hasNext())
          arrayList.add(c(iterator.next())); 
        return arrayList;
      } 
      return null;
    }
    
    public int describeContents() {
      return 0;
    }
    
    public MediaDescriptionCompat g() {
      return this.a;
    }
    
    public long h() {
      return this.b;
    }
    
    public String toString() {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("MediaSession.QueueItem {Description=");
      stringBuilder.append(this.a);
      stringBuilder.append(", Id=");
      stringBuilder.append(this.b);
      stringBuilder.append(" }");
      return stringBuilder.toString();
    }
    
    public void writeToParcel(Parcel param1Parcel, int param1Int) {
      this.a.writeToParcel(param1Parcel, param1Int);
      param1Parcel.writeLong(this.b);
    }
    
    public class a implements Parcelable.Creator<QueueItem> {
      public MediaSessionCompat.QueueItem a(Parcel param2Parcel) {
        return new MediaSessionCompat.QueueItem(param2Parcel);
      }
      
      public MediaSessionCompat.QueueItem[] b(int param2Int) {
        return new MediaSessionCompat.QueueItem[param2Int];
      }
    }
    
    public static class b {
      public static MediaSession.QueueItem a(MediaDescription param2MediaDescription, long param2Long) {
        return new MediaSession.QueueItem(param2MediaDescription, param2Long);
      }
      
      public static MediaDescription b(MediaSession.QueueItem param2QueueItem) {
        return param2QueueItem.getDescription();
      }
      
      public static long c(MediaSession.QueueItem param2QueueItem) {
        return param2QueueItem.getQueueId();
      }
    }
  }
  
  public class a implements Parcelable.Creator<QueueItem> {
    public MediaSessionCompat.QueueItem a(Parcel param1Parcel) {
      return new MediaSessionCompat.QueueItem(param1Parcel);
    }
    
    public MediaSessionCompat.QueueItem[] b(int param1Int) {
      return new MediaSessionCompat.QueueItem[param1Int];
    }
  }
  
  public static class b {
    public static MediaSession.QueueItem a(MediaDescription param1MediaDescription, long param1Long) {
      return new MediaSession.QueueItem(param1MediaDescription, param1Long);
    }
    
    public static MediaDescription b(MediaSession.QueueItem param1QueueItem) {
      return param1QueueItem.getDescription();
    }
    
    public static long c(MediaSession.QueueItem param1QueueItem) {
      return param1QueueItem.getQueueId();
    }
  }
  
  public static final class ResultReceiverWrapper implements Parcelable {
    public static final Parcelable.Creator<ResultReceiverWrapper> CREATOR = new a();
    
    public ResultReceiver a;
    
    public ResultReceiverWrapper(Parcel param1Parcel) {
      this.a = (ResultReceiver)ResultReceiver.CREATOR.createFromParcel(param1Parcel);
    }
    
    public int describeContents() {
      return 0;
    }
    
    public void writeToParcel(Parcel param1Parcel, int param1Int) {
      this.a.writeToParcel(param1Parcel, param1Int);
    }
    
    public class a implements Parcelable.Creator<ResultReceiverWrapper> {
      public MediaSessionCompat.ResultReceiverWrapper a(Parcel param2Parcel) {
        return new MediaSessionCompat.ResultReceiverWrapper(param2Parcel);
      }
      
      public MediaSessionCompat.ResultReceiverWrapper[] b(int param2Int) {
        return new MediaSessionCompat.ResultReceiverWrapper[param2Int];
      }
    }
  }
  
  public class a implements Parcelable.Creator<ResultReceiverWrapper> {
    public MediaSessionCompat.ResultReceiverWrapper a(Parcel param1Parcel) {
      return new MediaSessionCompat.ResultReceiverWrapper(param1Parcel);
    }
    
    public MediaSessionCompat.ResultReceiverWrapper[] b(int param1Int) {
      return new MediaSessionCompat.ResultReceiverWrapper[param1Int];
    }
  }
  
  public static final class Token implements Parcelable {
    public static final Parcelable.Creator<Token> CREATOR = new a();
    
    public final Object a = new Object();
    
    public final Object b;
    
    public b c;
    
    public m5.d d;
    
    public Token(Object param1Object) {
      this(param1Object, null, null);
    }
    
    public Token(Object param1Object, b param1b, m5.d param1d) {
      this.b = param1Object;
      this.c = param1b;
      this.d = param1d;
    }
    
    public b c() {
      synchronized (this.a) {
        return this.c;
      } 
    }
    
    public m5.d d() {
      synchronized (this.a) {
        return this.d;
      } 
    }
    
    public int describeContents() {
      return 0;
    }
    
    public boolean equals(Object param1Object) {
      boolean bool = true;
      if (this == param1Object)
        return true; 
      if (!(param1Object instanceof Token))
        return false; 
      Token token = (Token)param1Object;
      param1Object = this.b;
      Object object = token.b;
      if (param1Object == null) {
        if (object != null)
          bool = false; 
        return bool;
      } 
      return (object == null) ? false : param1Object.equals(object);
    }
    
    public Object g() {
      return this.b;
    }
    
    public void h(b param1b) {
      synchronized (this.a) {
        this.c = param1b;
        return;
      } 
    }
    
    public int hashCode() {
      Object object = this.b;
      return (object == null) ? 0 : object.hashCode();
    }
    
    public void i(m5.d param1d) {
      synchronized (this.a) {
        this.d = param1d;
        return;
      } 
    }
    
    public void writeToParcel(Parcel param1Parcel, int param1Int) {
      param1Parcel.writeParcelable((Parcelable)this.b, param1Int);
    }
    
    public class a implements Parcelable.Creator<Token> {
      public MediaSessionCompat.Token a(Parcel param2Parcel) {
        return new MediaSessionCompat.Token(param2Parcel.readParcelable(null));
      }
      
      public MediaSessionCompat.Token[] b(int param2Int) {
        return new MediaSessionCompat.Token[param2Int];
      }
    }
  }
  
  public class a implements Parcelable.Creator<Token> {
    public MediaSessionCompat.Token a(Parcel param1Parcel) {
      return new MediaSessionCompat.Token(param1Parcel.readParcelable(null));
    }
    
    public MediaSessionCompat.Token[] b(int param1Int) {
      return new MediaSessionCompat.Token[param1Int];
    }
  }
  
  public class a extends b {
    public final MediaSessionCompat f;
    
    public a(MediaSessionCompat this$0) {}
  }
  
  public static abstract class b {
    public final Object a = new Object();
    
    public final MediaSession.Callback b = new b(this);
    
    public boolean c;
    
    public WeakReference<MediaSessionCompat.c> d = new WeakReference<MediaSessionCompat.c>(null);
    
    public a e;
    
    public void A0() {}
    
    public void B(MediaSessionCompat.c param1c, Handler param1Handler) {
      boolean bool1;
      boolean bool2;
      long l;
      if (!this.c)
        return; 
      boolean bool3 = false;
      this.c = false;
      param1Handler.removeMessages(1);
      PlaybackStateCompat playbackStateCompat = param1c.g();
      if (playbackStateCompat == null) {
        l = 0L;
      } else {
        l = playbackStateCompat.d();
      } 
      if (playbackStateCompat != null && playbackStateCompat.l() == 3) {
        bool1 = true;
      } else {
        bool1 = false;
      } 
      if ((0x204L & l) != 0L) {
        bool2 = true;
      } else {
        bool2 = false;
      } 
      if ((l & 0x202L) != 0L)
        bool3 = true; 
      if (bool1 && bool3) {
        e0();
      } else if (!bool1 && bool2) {
        f0();
      } 
    }
    
    public void B0() {}
    
    public void C0(long param1Long) {}
    
    public void D0() {}
    
    public void E0(MediaSessionCompat.c param1c, Handler param1Handler) {
      synchronized (this.a) {
        WeakReference<MediaSessionCompat.c> weakReference = new WeakReference();
        this((T)param1c);
        this.d = weakReference;
        a a1 = this.e;
        a a2 = null;
        if (a1 != null)
          a1.removeCallbacksAndMessages(null); 
        a1 = a2;
        if (param1c != null)
          if (param1Handler == null) {
            a1 = a2;
          } else {
            a1 = new a(this, param1Handler.getLooper());
          }  
        this.e = a1;
        return;
      } 
    }
    
    public void Q(MediaDescriptionCompat param1MediaDescriptionCompat) {}
    
    public void R(MediaDescriptionCompat param1MediaDescriptionCompat, int param1Int) {}
    
    public void W(String param1String, Bundle param1Bundle, ResultReceiver param1ResultReceiver) {}
    
    public void a0(String param1String, Bundle param1Bundle) {}
    
    public void b0() {}
    
    public boolean c0(Intent param1Intent) {
      if (Build.VERSION.SDK_INT >= 27)
        return false; 
      synchronized (this.a) {
        MediaSessionCompat.c c = this.d.get();
        a a1 = this.e;
        if (c != null && a1 != null) {
          KeyEvent keyEvent = (KeyEvent)param1Intent.getParcelableExtra("android.intent.extra.KEY_EVENT");
          if (keyEvent != null && keyEvent.getAction() == 0) {
            null = c.m();
            int i = keyEvent.getKeyCode();
            if (i != 79 && i != 85) {
              B(c, a1);
              return false;
            } 
            if (keyEvent.getRepeatCount() == 0) {
              if (this.c) {
                long l;
                a1.removeMessages(1);
                this.c = false;
                PlaybackStateCompat playbackStateCompat = c.g();
                if (playbackStateCompat == null) {
                  l = 0L;
                } else {
                  l = playbackStateCompat.d();
                } 
                if ((l & 0x20L) != 0L)
                  A0(); 
              } else {
                this.c = true;
                a1.sendMessageDelayed(a1.obtainMessage(1, null), ViewConfiguration.getDoubleTapTimeout());
              } 
            } else {
              B(c, a1);
            } 
            return true;
          } 
        } 
        return false;
      } 
    }
    
    public void e0() {}
    
    public void f0() {}
    
    public void g0(String param1String, Bundle param1Bundle) {}
    
    public void l0(String param1String, Bundle param1Bundle) {}
    
    public void m0(Uri param1Uri, Bundle param1Bundle) {}
    
    public void n0() {}
    
    public void o0(String param1String, Bundle param1Bundle) {}
    
    public void p0(String param1String, Bundle param1Bundle) {}
    
    public void q0(Uri param1Uri, Bundle param1Bundle) {}
    
    public void r0(MediaDescriptionCompat param1MediaDescriptionCompat) {}
    
    public void s0() {}
    
    public void t0(long param1Long) {}
    
    public void u0(boolean param1Boolean) {}
    
    public void v0(float param1Float) {}
    
    public void w0(RatingCompat param1RatingCompat) {}
    
    public void x0(RatingCompat param1RatingCompat, Bundle param1Bundle) {}
    
    public void y0(int param1Int) {}
    
    public void z0(int param1Int) {}
    
    public class a extends Handler {
      public final MediaSessionCompat.b a;
      
      public a(MediaSessionCompat.b this$0, Looper param2Looper) {
        super(param2Looper);
      }
      
      public void handleMessage(Message param2Message) {
        if (param2Message.what == 1)
          synchronized (this.a.a) {
            MediaSessionCompat.c c = this.a.d.get();
            MediaSessionCompat.b b1 = this.a;
            a a1 = b1.e;
            if (c == null || b1 != c.e() || a1 == null)
              return; 
            c.l((i2.a)param2Message.obj);
            this.a.B(c, a1);
            c.l(null);
          }  
      }
    }
    
    public class b extends MediaSession.Callback {
      public final MediaSessionCompat.b a;
      
      public b(MediaSessionCompat.b this$0) {}
      
      public final void a(MediaSessionCompat.c param2c) {
        param2c.l(null);
      }
      
      public final MediaSessionCompat.d b() {
        synchronized (this.a.a) {
          MediaSessionCompat.d d = (MediaSessionCompat.d)this.a.d.get();
          if (d == null || this.a != d.e())
            d = null; 
          return d;
        } 
      }
      
      public final void c(MediaSessionCompat.c param2c) {
        if (Build.VERSION.SDK_INT >= 28)
          return; 
        String str2 = param2c.c();
        String str1 = str2;
        if (TextUtils.isEmpty(str2))
          str1 = "android.media.session.MediaController"; 
        param2c.l(new i2.a(str1, -1, -1));
      }
      
      public void onCommand(String param2String, Bundle param2Bundle, ResultReceiver param2ResultReceiver) {
        // Byte code:
        //   0: aload_0
        //   1: invokevirtual b : ()Landroid/support/v4/media/session/MediaSessionCompat$d;
        //   4: astore #8
        //   6: aload #8
        //   8: ifnonnull -> 12
        //   11: return
        //   12: aload_2
        //   13: invokestatic a : (Landroid/os/Bundle;)V
        //   16: aload_0
        //   17: aload #8
        //   19: invokevirtual c : (Landroid/support/v4/media/session/MediaSessionCompat$c;)V
        //   22: aload_1
        //   23: ldc 'android.support.v4.media.session.command.GET_EXTRA_BINDER'
        //   25: invokevirtual equals : (Ljava/lang/Object;)Z
        //   28: istore #5
        //   30: aconst_null
        //   31: astore #7
        //   33: aconst_null
        //   34: astore #6
        //   36: iload #5
        //   38: ifeq -> 106
        //   41: new android/os/Bundle
        //   44: astore_2
        //   45: aload_2
        //   46: invokespecial <init> : ()V
        //   49: aload #8
        //   51: invokevirtual b : ()Landroid/support/v4/media/session/MediaSessionCompat$Token;
        //   54: astore #7
        //   56: aload #7
        //   58: invokevirtual c : ()Landroid/support/v4/media/session/b;
        //   61: astore_1
        //   62: aload_1
        //   63: ifnonnull -> 72
        //   66: aload #6
        //   68: astore_1
        //   69: goto -> 79
        //   72: aload_1
        //   73: invokeinterface asBinder : ()Landroid/os/IBinder;
        //   78: astore_1
        //   79: aload_2
        //   80: ldc 'android.support.v4.media.session.EXTRA_BINDER'
        //   82: aload_1
        //   83: invokestatic b : (Landroid/os/Bundle;Ljava/lang/String;Landroid/os/IBinder;)V
        //   86: aload_2
        //   87: ldc 'android.support.v4.media.session.SESSION_TOKEN2'
        //   89: aload #7
        //   91: invokevirtual d : ()Lm5/d;
        //   94: invokestatic c : (Landroid/os/Bundle;Ljava/lang/String;Lm5/d;)V
        //   97: aload_3
        //   98: iconst_0
        //   99: aload_2
        //   100: invokevirtual send : (ILandroid/os/Bundle;)V
        //   103: goto -> 319
        //   106: aload_1
        //   107: ldc 'android.support.v4.media.session.command.ADD_QUEUE_ITEM'
        //   109: invokevirtual equals : (Ljava/lang/Object;)Z
        //   112: istore #5
        //   114: iload #5
        //   116: ifeq -> 138
        //   119: aload_0
        //   120: getfield a : Landroid/support/v4/media/session/MediaSessionCompat$b;
        //   123: aload_2
        //   124: ldc 'android.support.v4.media.session.command.ARGUMENT_MEDIA_DESCRIPTION'
        //   126: invokevirtual getParcelable : (Ljava/lang/String;)Landroid/os/Parcelable;
        //   129: checkcast android/support/v4/media/MediaDescriptionCompat
        //   132: invokevirtual Q : (Landroid/support/v4/media/MediaDescriptionCompat;)V
        //   135: goto -> 319
        //   138: aload_1
        //   139: ldc 'android.support.v4.media.session.command.ADD_QUEUE_ITEM_AT'
        //   141: invokevirtual equals : (Ljava/lang/Object;)Z
        //   144: istore #5
        //   146: iload #5
        //   148: ifeq -> 176
        //   151: aload_0
        //   152: getfield a : Landroid/support/v4/media/session/MediaSessionCompat$b;
        //   155: aload_2
        //   156: ldc 'android.support.v4.media.session.command.ARGUMENT_MEDIA_DESCRIPTION'
        //   158: invokevirtual getParcelable : (Ljava/lang/String;)Landroid/os/Parcelable;
        //   161: checkcast android/support/v4/media/MediaDescriptionCompat
        //   164: aload_2
        //   165: ldc 'android.support.v4.media.session.command.ARGUMENT_INDEX'
        //   167: invokevirtual getInt : (Ljava/lang/String;)I
        //   170: invokevirtual R : (Landroid/support/v4/media/MediaDescriptionCompat;I)V
        //   173: goto -> 319
        //   176: aload_1
        //   177: ldc 'android.support.v4.media.session.command.REMOVE_QUEUE_ITEM'
        //   179: invokevirtual equals : (Ljava/lang/Object;)Z
        //   182: ifeq -> 208
        //   185: aload_0
        //   186: getfield a : Landroid/support/v4/media/session/MediaSessionCompat$b;
        //   189: astore_1
        //   190: aload_2
        //   191: ldc 'android.support.v4.media.session.command.ARGUMENT_MEDIA_DESCRIPTION'
        //   193: invokevirtual getParcelable : (Ljava/lang/String;)Landroid/os/Parcelable;
        //   196: checkcast android/support/v4/media/MediaDescriptionCompat
        //   199: astore_2
        //   200: aload_1
        //   201: aload_2
        //   202: invokevirtual r0 : (Landroid/support/v4/media/MediaDescriptionCompat;)V
        //   205: goto -> 319
        //   208: aload_1
        //   209: ldc 'android.support.v4.media.session.command.REMOVE_QUEUE_ITEM_AT'
        //   211: invokevirtual equals : (Ljava/lang/Object;)Z
        //   214: ifeq -> 297
        //   217: aload #8
        //   219: getfield h : Ljava/util/List;
        //   222: ifnull -> 319
        //   225: aload_2
        //   226: ldc 'android.support.v4.media.session.command.ARGUMENT_INDEX'
        //   228: iconst_m1
        //   229: invokevirtual getInt : (Ljava/lang/String;I)I
        //   232: istore #4
        //   234: aload #7
        //   236: astore_1
        //   237: iload #4
        //   239: iflt -> 276
        //   242: aload #7
        //   244: astore_1
        //   245: iload #4
        //   247: aload #8
        //   249: getfield h : Ljava/util/List;
        //   252: invokeinterface size : ()I
        //   257: if_icmpge -> 276
        //   260: aload #8
        //   262: getfield h : Ljava/util/List;
        //   265: iload #4
        //   267: invokeinterface get : (I)Ljava/lang/Object;
        //   272: checkcast android/support/v4/media/session/MediaSessionCompat$QueueItem
        //   275: astore_1
        //   276: aload_1
        //   277: ifnull -> 319
        //   280: aload_0
        //   281: getfield a : Landroid/support/v4/media/session/MediaSessionCompat$b;
        //   284: astore_2
        //   285: aload_1
        //   286: invokevirtual g : ()Landroid/support/v4/media/MediaDescriptionCompat;
        //   289: astore_3
        //   290: aload_2
        //   291: astore_1
        //   292: aload_3
        //   293: astore_2
        //   294: goto -> 200
        //   297: aload_0
        //   298: getfield a : Landroid/support/v4/media/session/MediaSessionCompat$b;
        //   301: aload_1
        //   302: aload_2
        //   303: aload_3
        //   304: invokevirtual W : (Ljava/lang/String;Landroid/os/Bundle;Landroid/os/ResultReceiver;)V
        //   307: goto -> 319
        //   310: astore_1
        //   311: ldc 'MediaSessionCompat'
        //   313: ldc 'Could not unparcel the extra data.'
        //   315: invokestatic e : (Ljava/lang/String;Ljava/lang/String;)I
        //   318: pop
        //   319: aload_0
        //   320: aload #8
        //   322: invokevirtual a : (Landroid/support/v4/media/session/MediaSessionCompat$c;)V
        //   325: return
        // Exception table:
        //   from	to	target	type
        //   22	30	310	android/os/BadParcelableException
        //   41	62	310	android/os/BadParcelableException
        //   72	79	310	android/os/BadParcelableException
        //   79	103	310	android/os/BadParcelableException
        //   106	114	310	android/os/BadParcelableException
        //   119	135	310	android/os/BadParcelableException
        //   138	146	310	android/os/BadParcelableException
        //   151	173	310	android/os/BadParcelableException
        //   176	200	310	android/os/BadParcelableException
        //   200	205	310	android/os/BadParcelableException
        //   208	234	310	android/os/BadParcelableException
        //   245	276	310	android/os/BadParcelableException
        //   280	290	310	android/os/BadParcelableException
        //   297	307	310	android/os/BadParcelableException
      }
      
      public void onCustomAction(String param2String, Bundle param2Bundle) {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        MediaSessionCompat.a(param2Bundle);
        c(d);
        try {
          Uri uri;
          boolean bool = param2String.equals("android.support.v4.media.session.action.PLAY_FROM_URI");
          if (bool) {
            uri = (Uri)param2Bundle.getParcelable("android.support.v4.media.session.action.ARGUMENT_URI");
            param2Bundle = param2Bundle.getBundle("android.support.v4.media.session.action.ARGUMENT_EXTRAS");
            MediaSessionCompat.a(param2Bundle);
            this.a.m0(uri, param2Bundle);
          } else if (uri.equals("android.support.v4.media.session.action.PREPARE")) {
            this.a.n0();
          } else {
            String str;
            if (uri.equals("android.support.v4.media.session.action.PREPARE_FROM_MEDIA_ID")) {
              str = param2Bundle.getString("android.support.v4.media.session.action.ARGUMENT_MEDIA_ID");
              param2Bundle = param2Bundle.getBundle("android.support.v4.media.session.action.ARGUMENT_EXTRAS");
              MediaSessionCompat.a(param2Bundle);
              this.a.o0(str, param2Bundle);
            } else if (str.equals("android.support.v4.media.session.action.PREPARE_FROM_SEARCH")) {
              str = param2Bundle.getString("android.support.v4.media.session.action.ARGUMENT_QUERY");
              param2Bundle = param2Bundle.getBundle("android.support.v4.media.session.action.ARGUMENT_EXTRAS");
              MediaSessionCompat.a(param2Bundle);
              this.a.p0(str, param2Bundle);
            } else {
              Uri uri1;
              if (str.equals("android.support.v4.media.session.action.PREPARE_FROM_URI")) {
                uri1 = (Uri)param2Bundle.getParcelable("android.support.v4.media.session.action.ARGUMENT_URI");
                param2Bundle = param2Bundle.getBundle("android.support.v4.media.session.action.ARGUMENT_EXTRAS");
                MediaSessionCompat.a(param2Bundle);
                this.a.q0(uri1, param2Bundle);
              } else if (uri1.equals("android.support.v4.media.session.action.SET_CAPTIONING_ENABLED")) {
                bool = param2Bundle.getBoolean("android.support.v4.media.session.action.ARGUMENT_CAPTIONING_ENABLED");
                this.a.u0(bool);
              } else if (uri1.equals("android.support.v4.media.session.action.SET_REPEAT_MODE")) {
                int i = param2Bundle.getInt("android.support.v4.media.session.action.ARGUMENT_REPEAT_MODE");
                this.a.y0(i);
              } else if (uri1.equals("android.support.v4.media.session.action.SET_SHUFFLE_MODE")) {
                int i = param2Bundle.getInt("android.support.v4.media.session.action.ARGUMENT_SHUFFLE_MODE");
                this.a.z0(i);
              } else {
                RatingCompat ratingCompat;
                if (uri1.equals("android.support.v4.media.session.action.SET_RATING")) {
                  ratingCompat = (RatingCompat)param2Bundle.getParcelable("android.support.v4.media.session.action.ARGUMENT_RATING");
                  param2Bundle = param2Bundle.getBundle("android.support.v4.media.session.action.ARGUMENT_EXTRAS");
                  MediaSessionCompat.a(param2Bundle);
                  this.a.x0(ratingCompat, param2Bundle);
                } else if (ratingCompat.equals("android.support.v4.media.session.action.SET_PLAYBACK_SPEED")) {
                  float f = param2Bundle.getFloat("android.support.v4.media.session.action.ARGUMENT_PLAYBACK_SPEED", 1.0F);
                  this.a.v0(f);
                } else {
                  this.a.a0((String)ratingCompat, param2Bundle);
                } 
              } 
            } 
          } 
        } catch (BadParcelableException badParcelableException) {
          Log.e("MediaSessionCompat", "Could not unparcel the data.");
        } 
        a(d);
      }
      
      public void onFastForward() {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        c(d);
        this.a.b0();
        a(d);
      }
      
      public boolean onMediaButtonEvent(Intent param2Intent) {
        MediaSessionCompat.d d = b();
        boolean bool = false;
        if (d == null)
          return false; 
        c(d);
        boolean bool1 = this.a.c0(param2Intent);
        a(d);
        if (bool1 || super.onMediaButtonEvent(param2Intent))
          bool = true; 
        return bool;
      }
      
      public void onPause() {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        c(d);
        this.a.e0();
        a(d);
      }
      
      public void onPlay() {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        c(d);
        this.a.f0();
        a(d);
      }
      
      public void onPlayFromMediaId(String param2String, Bundle param2Bundle) {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        MediaSessionCompat.a(param2Bundle);
        c(d);
        this.a.g0(param2String, param2Bundle);
        a(d);
      }
      
      public void onPlayFromSearch(String param2String, Bundle param2Bundle) {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        MediaSessionCompat.a(param2Bundle);
        c(d);
        this.a.l0(param2String, param2Bundle);
        a(d);
      }
      
      public void onPlayFromUri(Uri param2Uri, Bundle param2Bundle) {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        MediaSessionCompat.a(param2Bundle);
        c(d);
        this.a.m0(param2Uri, param2Bundle);
        a(d);
      }
      
      public void onPrepare() {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        c(d);
        this.a.n0();
        a(d);
      }
      
      public void onPrepareFromMediaId(String param2String, Bundle param2Bundle) {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        MediaSessionCompat.a(param2Bundle);
        c(d);
        this.a.o0(param2String, param2Bundle);
        a(d);
      }
      
      public void onPrepareFromSearch(String param2String, Bundle param2Bundle) {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        MediaSessionCompat.a(param2Bundle);
        c(d);
        this.a.p0(param2String, param2Bundle);
        a(d);
      }
      
      public void onPrepareFromUri(Uri param2Uri, Bundle param2Bundle) {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        MediaSessionCompat.a(param2Bundle);
        c(d);
        this.a.q0(param2Uri, param2Bundle);
        a(d);
      }
      
      public void onRewind() {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        c(d);
        this.a.s0();
        a(d);
      }
      
      public void onSeekTo(long param2Long) {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        c(d);
        this.a.t0(param2Long);
        a(d);
      }
      
      public void onSetPlaybackSpeed(float param2Float) {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        c(d);
        this.a.v0(param2Float);
        a(d);
      }
      
      public void onSetRating(Rating param2Rating) {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        c(d);
        this.a.w0(RatingCompat.c(param2Rating));
        a(d);
      }
      
      public void onSkipToNext() {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        c(d);
        this.a.A0();
        a(d);
      }
      
      public void onSkipToPrevious() {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        c(d);
        this.a.B0();
        a(d);
      }
      
      public void onSkipToQueueItem(long param2Long) {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        c(d);
        this.a.C0(param2Long);
        a(d);
      }
      
      public void onStop() {
        MediaSessionCompat.d d = b();
        if (d == null)
          return; 
        c(d);
        this.a.D0();
        a(d);
      }
    }
  }
  
  public class a extends Handler {
    public final MediaSessionCompat.b a;
    
    public a(MediaSessionCompat this$0, Looper param1Looper) {
      super(param1Looper);
    }
    
    public void handleMessage(Message param1Message) {
      if (param1Message.what == 1)
        synchronized (this.a.a) {
          MediaSessionCompat.c c = this.a.d.get();
          MediaSessionCompat.b b1 = this.a;
          a a1 = b1.e;
          if (c == null || b1 != c.e() || a1 == null)
            return; 
          c.l((i2.a)param1Message.obj);
          this.a.B(c, a1);
          c.l(null);
        }  
    }
  }
  
  public class b extends MediaSession.Callback {
    public final MediaSessionCompat.b a;
    
    public b(MediaSessionCompat this$0) {}
    
    public final void a(MediaSessionCompat.c param1c) {
      param1c.l(null);
    }
    
    public final MediaSessionCompat.d b() {
      synchronized (this.a.a) {
        MediaSessionCompat.d d = (MediaSessionCompat.d)this.a.d.get();
        if (d == null || this.a != d.e())
          d = null; 
        return d;
      } 
    }
    
    public final void c(MediaSessionCompat.c param1c) {
      if (Build.VERSION.SDK_INT >= 28)
        return; 
      String str2 = param1c.c();
      String str1 = str2;
      if (TextUtils.isEmpty(str2))
        str1 = "android.media.session.MediaController"; 
      param1c.l(new i2.a(str1, -1, -1));
    }
    
    public void onCommand(String param1String, Bundle param1Bundle, ResultReceiver param1ResultReceiver) {
      // Byte code:
      //   0: aload_0
      //   1: invokevirtual b : ()Landroid/support/v4/media/session/MediaSessionCompat$d;
      //   4: astore #8
      //   6: aload #8
      //   8: ifnonnull -> 12
      //   11: return
      //   12: aload_2
      //   13: invokestatic a : (Landroid/os/Bundle;)V
      //   16: aload_0
      //   17: aload #8
      //   19: invokevirtual c : (Landroid/support/v4/media/session/MediaSessionCompat$c;)V
      //   22: aload_1
      //   23: ldc 'android.support.v4.media.session.command.GET_EXTRA_BINDER'
      //   25: invokevirtual equals : (Ljava/lang/Object;)Z
      //   28: istore #5
      //   30: aconst_null
      //   31: astore #7
      //   33: aconst_null
      //   34: astore #6
      //   36: iload #5
      //   38: ifeq -> 106
      //   41: new android/os/Bundle
      //   44: astore_2
      //   45: aload_2
      //   46: invokespecial <init> : ()V
      //   49: aload #8
      //   51: invokevirtual b : ()Landroid/support/v4/media/session/MediaSessionCompat$Token;
      //   54: astore #7
      //   56: aload #7
      //   58: invokevirtual c : ()Landroid/support/v4/media/session/b;
      //   61: astore_1
      //   62: aload_1
      //   63: ifnonnull -> 72
      //   66: aload #6
      //   68: astore_1
      //   69: goto -> 79
      //   72: aload_1
      //   73: invokeinterface asBinder : ()Landroid/os/IBinder;
      //   78: astore_1
      //   79: aload_2
      //   80: ldc 'android.support.v4.media.session.EXTRA_BINDER'
      //   82: aload_1
      //   83: invokestatic b : (Landroid/os/Bundle;Ljava/lang/String;Landroid/os/IBinder;)V
      //   86: aload_2
      //   87: ldc 'android.support.v4.media.session.SESSION_TOKEN2'
      //   89: aload #7
      //   91: invokevirtual d : ()Lm5/d;
      //   94: invokestatic c : (Landroid/os/Bundle;Ljava/lang/String;Lm5/d;)V
      //   97: aload_3
      //   98: iconst_0
      //   99: aload_2
      //   100: invokevirtual send : (ILandroid/os/Bundle;)V
      //   103: goto -> 319
      //   106: aload_1
      //   107: ldc 'android.support.v4.media.session.command.ADD_QUEUE_ITEM'
      //   109: invokevirtual equals : (Ljava/lang/Object;)Z
      //   112: istore #5
      //   114: iload #5
      //   116: ifeq -> 138
      //   119: aload_0
      //   120: getfield a : Landroid/support/v4/media/session/MediaSessionCompat$b;
      //   123: aload_2
      //   124: ldc 'android.support.v4.media.session.command.ARGUMENT_MEDIA_DESCRIPTION'
      //   126: invokevirtual getParcelable : (Ljava/lang/String;)Landroid/os/Parcelable;
      //   129: checkcast android/support/v4/media/MediaDescriptionCompat
      //   132: invokevirtual Q : (Landroid/support/v4/media/MediaDescriptionCompat;)V
      //   135: goto -> 319
      //   138: aload_1
      //   139: ldc 'android.support.v4.media.session.command.ADD_QUEUE_ITEM_AT'
      //   141: invokevirtual equals : (Ljava/lang/Object;)Z
      //   144: istore #5
      //   146: iload #5
      //   148: ifeq -> 176
      //   151: aload_0
      //   152: getfield a : Landroid/support/v4/media/session/MediaSessionCompat$b;
      //   155: aload_2
      //   156: ldc 'android.support.v4.media.session.command.ARGUMENT_MEDIA_DESCRIPTION'
      //   158: invokevirtual getParcelable : (Ljava/lang/String;)Landroid/os/Parcelable;
      //   161: checkcast android/support/v4/media/MediaDescriptionCompat
      //   164: aload_2
      //   165: ldc 'android.support.v4.media.session.command.ARGUMENT_INDEX'
      //   167: invokevirtual getInt : (Ljava/lang/String;)I
      //   170: invokevirtual R : (Landroid/support/v4/media/MediaDescriptionCompat;I)V
      //   173: goto -> 319
      //   176: aload_1
      //   177: ldc 'android.support.v4.media.session.command.REMOVE_QUEUE_ITEM'
      //   179: invokevirtual equals : (Ljava/lang/Object;)Z
      //   182: ifeq -> 208
      //   185: aload_0
      //   186: getfield a : Landroid/support/v4/media/session/MediaSessionCompat$b;
      //   189: astore_1
      //   190: aload_2
      //   191: ldc 'android.support.v4.media.session.command.ARGUMENT_MEDIA_DESCRIPTION'
      //   193: invokevirtual getParcelable : (Ljava/lang/String;)Landroid/os/Parcelable;
      //   196: checkcast android/support/v4/media/MediaDescriptionCompat
      //   199: astore_2
      //   200: aload_1
      //   201: aload_2
      //   202: invokevirtual r0 : (Landroid/support/v4/media/MediaDescriptionCompat;)V
      //   205: goto -> 319
      //   208: aload_1
      //   209: ldc 'android.support.v4.media.session.command.REMOVE_QUEUE_ITEM_AT'
      //   211: invokevirtual equals : (Ljava/lang/Object;)Z
      //   214: ifeq -> 297
      //   217: aload #8
      //   219: getfield h : Ljava/util/List;
      //   222: ifnull -> 319
      //   225: aload_2
      //   226: ldc 'android.support.v4.media.session.command.ARGUMENT_INDEX'
      //   228: iconst_m1
      //   229: invokevirtual getInt : (Ljava/lang/String;I)I
      //   232: istore #4
      //   234: aload #7
      //   236: astore_1
      //   237: iload #4
      //   239: iflt -> 276
      //   242: aload #7
      //   244: astore_1
      //   245: iload #4
      //   247: aload #8
      //   249: getfield h : Ljava/util/List;
      //   252: invokeinterface size : ()I
      //   257: if_icmpge -> 276
      //   260: aload #8
      //   262: getfield h : Ljava/util/List;
      //   265: iload #4
      //   267: invokeinterface get : (I)Ljava/lang/Object;
      //   272: checkcast android/support/v4/media/session/MediaSessionCompat$QueueItem
      //   275: astore_1
      //   276: aload_1
      //   277: ifnull -> 319
      //   280: aload_0
      //   281: getfield a : Landroid/support/v4/media/session/MediaSessionCompat$b;
      //   284: astore_2
      //   285: aload_1
      //   286: invokevirtual g : ()Landroid/support/v4/media/MediaDescriptionCompat;
      //   289: astore_3
      //   290: aload_2
      //   291: astore_1
      //   292: aload_3
      //   293: astore_2
      //   294: goto -> 200
      //   297: aload_0
      //   298: getfield a : Landroid/support/v4/media/session/MediaSessionCompat$b;
      //   301: aload_1
      //   302: aload_2
      //   303: aload_3
      //   304: invokevirtual W : (Ljava/lang/String;Landroid/os/Bundle;Landroid/os/ResultReceiver;)V
      //   307: goto -> 319
      //   310: astore_1
      //   311: ldc 'MediaSessionCompat'
      //   313: ldc 'Could not unparcel the extra data.'
      //   315: invokestatic e : (Ljava/lang/String;Ljava/lang/String;)I
      //   318: pop
      //   319: aload_0
      //   320: aload #8
      //   322: invokevirtual a : (Landroid/support/v4/media/session/MediaSessionCompat$c;)V
      //   325: return
      // Exception table:
      //   from	to	target	type
      //   22	30	310	android/os/BadParcelableException
      //   41	62	310	android/os/BadParcelableException
      //   72	79	310	android/os/BadParcelableException
      //   79	103	310	android/os/BadParcelableException
      //   106	114	310	android/os/BadParcelableException
      //   119	135	310	android/os/BadParcelableException
      //   138	146	310	android/os/BadParcelableException
      //   151	173	310	android/os/BadParcelableException
      //   176	200	310	android/os/BadParcelableException
      //   200	205	310	android/os/BadParcelableException
      //   208	234	310	android/os/BadParcelableException
      //   245	276	310	android/os/BadParcelableException
      //   280	290	310	android/os/BadParcelableException
      //   297	307	310	android/os/BadParcelableException
    }
    
    public void onCustomAction(String param1String, Bundle param1Bundle) {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      MediaSessionCompat.a(param1Bundle);
      c(d);
      try {
        Uri uri;
        boolean bool = param1String.equals("android.support.v4.media.session.action.PLAY_FROM_URI");
        if (bool) {
          uri = (Uri)param1Bundle.getParcelable("android.support.v4.media.session.action.ARGUMENT_URI");
          param1Bundle = param1Bundle.getBundle("android.support.v4.media.session.action.ARGUMENT_EXTRAS");
          MediaSessionCompat.a(param1Bundle);
          this.a.m0(uri, param1Bundle);
        } else if (uri.equals("android.support.v4.media.session.action.PREPARE")) {
          this.a.n0();
        } else {
          String str;
          if (uri.equals("android.support.v4.media.session.action.PREPARE_FROM_MEDIA_ID")) {
            str = param1Bundle.getString("android.support.v4.media.session.action.ARGUMENT_MEDIA_ID");
            param1Bundle = param1Bundle.getBundle("android.support.v4.media.session.action.ARGUMENT_EXTRAS");
            MediaSessionCompat.a(param1Bundle);
            this.a.o0(str, param1Bundle);
          } else if (str.equals("android.support.v4.media.session.action.PREPARE_FROM_SEARCH")) {
            str = param1Bundle.getString("android.support.v4.media.session.action.ARGUMENT_QUERY");
            param1Bundle = param1Bundle.getBundle("android.support.v4.media.session.action.ARGUMENT_EXTRAS");
            MediaSessionCompat.a(param1Bundle);
            this.a.p0(str, param1Bundle);
          } else {
            Uri uri1;
            if (str.equals("android.support.v4.media.session.action.PREPARE_FROM_URI")) {
              uri1 = (Uri)param1Bundle.getParcelable("android.support.v4.media.session.action.ARGUMENT_URI");
              param1Bundle = param1Bundle.getBundle("android.support.v4.media.session.action.ARGUMENT_EXTRAS");
              MediaSessionCompat.a(param1Bundle);
              this.a.q0(uri1, param1Bundle);
            } else if (uri1.equals("android.support.v4.media.session.action.SET_CAPTIONING_ENABLED")) {
              bool = param1Bundle.getBoolean("android.support.v4.media.session.action.ARGUMENT_CAPTIONING_ENABLED");
              this.a.u0(bool);
            } else if (uri1.equals("android.support.v4.media.session.action.SET_REPEAT_MODE")) {
              int i = param1Bundle.getInt("android.support.v4.media.session.action.ARGUMENT_REPEAT_MODE");
              this.a.y0(i);
            } else if (uri1.equals("android.support.v4.media.session.action.SET_SHUFFLE_MODE")) {
              int i = param1Bundle.getInt("android.support.v4.media.session.action.ARGUMENT_SHUFFLE_MODE");
              this.a.z0(i);
            } else {
              RatingCompat ratingCompat;
              if (uri1.equals("android.support.v4.media.session.action.SET_RATING")) {
                ratingCompat = (RatingCompat)param1Bundle.getParcelable("android.support.v4.media.session.action.ARGUMENT_RATING");
                param1Bundle = param1Bundle.getBundle("android.support.v4.media.session.action.ARGUMENT_EXTRAS");
                MediaSessionCompat.a(param1Bundle);
                this.a.x0(ratingCompat, param1Bundle);
              } else if (ratingCompat.equals("android.support.v4.media.session.action.SET_PLAYBACK_SPEED")) {
                float f = param1Bundle.getFloat("android.support.v4.media.session.action.ARGUMENT_PLAYBACK_SPEED", 1.0F);
                this.a.v0(f);
              } else {
                this.a.a0((String)ratingCompat, param1Bundle);
              } 
            } 
          } 
        } 
      } catch (BadParcelableException badParcelableException) {
        Log.e("MediaSessionCompat", "Could not unparcel the data.");
      } 
      a(d);
    }
    
    public void onFastForward() {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      c(d);
      this.a.b0();
      a(d);
    }
    
    public boolean onMediaButtonEvent(Intent param1Intent) {
      MediaSessionCompat.d d = b();
      boolean bool = false;
      if (d == null)
        return false; 
      c(d);
      boolean bool1 = this.a.c0(param1Intent);
      a(d);
      if (bool1 || super.onMediaButtonEvent(param1Intent))
        bool = true; 
      return bool;
    }
    
    public void onPause() {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      c(d);
      this.a.e0();
      a(d);
    }
    
    public void onPlay() {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      c(d);
      this.a.f0();
      a(d);
    }
    
    public void onPlayFromMediaId(String param1String, Bundle param1Bundle) {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      MediaSessionCompat.a(param1Bundle);
      c(d);
      this.a.g0(param1String, param1Bundle);
      a(d);
    }
    
    public void onPlayFromSearch(String param1String, Bundle param1Bundle) {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      MediaSessionCompat.a(param1Bundle);
      c(d);
      this.a.l0(param1String, param1Bundle);
      a(d);
    }
    
    public void onPlayFromUri(Uri param1Uri, Bundle param1Bundle) {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      MediaSessionCompat.a(param1Bundle);
      c(d);
      this.a.m0(param1Uri, param1Bundle);
      a(d);
    }
    
    public void onPrepare() {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      c(d);
      this.a.n0();
      a(d);
    }
    
    public void onPrepareFromMediaId(String param1String, Bundle param1Bundle) {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      MediaSessionCompat.a(param1Bundle);
      c(d);
      this.a.o0(param1String, param1Bundle);
      a(d);
    }
    
    public void onPrepareFromSearch(String param1String, Bundle param1Bundle) {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      MediaSessionCompat.a(param1Bundle);
      c(d);
      this.a.p0(param1String, param1Bundle);
      a(d);
    }
    
    public void onPrepareFromUri(Uri param1Uri, Bundle param1Bundle) {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      MediaSessionCompat.a(param1Bundle);
      c(d);
      this.a.q0(param1Uri, param1Bundle);
      a(d);
    }
    
    public void onRewind() {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      c(d);
      this.a.s0();
      a(d);
    }
    
    public void onSeekTo(long param1Long) {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      c(d);
      this.a.t0(param1Long);
      a(d);
    }
    
    public void onSetPlaybackSpeed(float param1Float) {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      c(d);
      this.a.v0(param1Float);
      a(d);
    }
    
    public void onSetRating(Rating param1Rating) {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      c(d);
      this.a.w0(RatingCompat.c(param1Rating));
      a(d);
    }
    
    public void onSkipToNext() {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      c(d);
      this.a.A0();
      a(d);
    }
    
    public void onSkipToPrevious() {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      c(d);
      this.a.B0();
      a(d);
    }
    
    public void onSkipToQueueItem(long param1Long) {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      c(d);
      this.a.C0(param1Long);
      a(d);
    }
    
    public void onStop() {
      MediaSessionCompat.d d = b();
      if (d == null)
        return; 
      c(d);
      this.a.D0();
      a(d);
    }
  }
  
  public static interface c {
    void a(int param1Int);
    
    MediaSessionCompat.Token b();
    
    String c();
    
    void d(MediaSessionCompat.b param1b, Handler param1Handler);
    
    MediaSessionCompat.b e();
    
    void f(MediaMetadataCompat param1MediaMetadataCompat);
    
    PlaybackStateCompat g();
    
    void h(PendingIntent param1PendingIntent);
    
    void i(boolean param1Boolean);
    
    void j(int param1Int);
    
    void k(PlaybackStateCompat param1PlaybackStateCompat);
    
    void l(i2.a param1a);
    
    i2.a m();
    
    void r(int param1Int);
    
    void release();
  }
  
  public static class d implements c {
    public final MediaSession a;
    
    public final MediaSessionCompat.Token b;
    
    public final Object c = new Object();
    
    public Bundle d;
    
    public boolean e = false;
    
    public final RemoteCallbackList<a> f = new RemoteCallbackList();
    
    public PlaybackStateCompat g;
    
    public List<MediaSessionCompat.QueueItem> h;
    
    public MediaMetadataCompat i;
    
    public int j;
    
    public boolean k;
    
    public int l;
    
    public int m;
    
    public MediaSessionCompat.b n;
    
    public i2.a o;
    
    public d(Context param1Context, String param1String, m5.d param1d, Bundle param1Bundle) {
      MediaSession mediaSession = n(param1Context, param1String, param1Bundle);
      this.a = mediaSession;
      this.b = new MediaSessionCompat.Token(mediaSession.getSessionToken(), new a(this), param1d);
      this.d = param1Bundle;
      a(3);
    }
    
    public void a(int param1Int) {
      this.a.setFlags(param1Int | 0x1 | 0x2);
    }
    
    public MediaSessionCompat.Token b() {
      return this.b;
    }
    
    public String c() {
      try {
        return (String)this.a.getClass().getMethod("getCallingPackage", new Class[0]).invoke(this.a, new Object[0]);
      } catch (Exception exception) {
        Log.e("MediaSessionCompat", "Cannot execute MediaSession.getCallingPackage()", exception);
        return null;
      } 
    }
    
    public void d(MediaSessionCompat.b param1b, Handler param1Handler) {
      synchronized (this.c) {
        MediaSession.Callback callback;
        this.n = param1b;
        MediaSession mediaSession = this.a;
        if (param1b == null) {
          callback = null;
        } else {
          callback = param1b.b;
        } 
        mediaSession.setCallback(callback, param1Handler);
        if (param1b != null)
          param1b.E0(this, param1Handler); 
        return;
      } 
    }
    
    public MediaSessionCompat.b e() {
      synchronized (this.c) {
        return this.n;
      } 
    }
    
    public void f(MediaMetadataCompat param1MediaMetadataCompat) {
      MediaMetadata mediaMetadata;
      this.i = param1MediaMetadataCompat;
      MediaSession mediaSession = this.a;
      if (param1MediaMetadataCompat == null) {
        param1MediaMetadataCompat = null;
      } else {
        mediaMetadata = (MediaMetadata)param1MediaMetadataCompat.i();
      } 
      mediaSession.setMetadata(mediaMetadata);
    }
    
    public PlaybackStateCompat g() {
      return this.g;
    }
    
    public void h(PendingIntent param1PendingIntent) {
      this.a.setMediaButtonReceiver(param1PendingIntent);
    }
    
    public void i(boolean param1Boolean) {
      this.a.setActive(param1Boolean);
    }
    
    public void j(int param1Int) {
      if (this.l != param1Int) {
        this.l = param1Int;
        int i = this.f.beginBroadcast() - 1;
        while (true) {
          if (i >= 0) {
            a a1 = (a)this.f.getBroadcastItem(i);
            try {
              a1.q(param1Int);
            } catch (RemoteException remoteException) {}
            i--;
            continue;
          } 
          this.f.finishBroadcast();
          return;
        } 
      } 
    }
    
    public void k(PlaybackStateCompat param1PlaybackStateCompat) {
      this.g = param1PlaybackStateCompat;
      int i = this.f.beginBroadcast() - 1;
      while (true) {
        PlaybackState playbackState;
        if (i >= 0) {
          a a1 = (a)this.f.getBroadcastItem(i);
          try {
            a1.U0(param1PlaybackStateCompat);
          } catch (RemoteException remoteException) {}
          i--;
          continue;
        } 
        this.f.finishBroadcast();
        MediaSession mediaSession = this.a;
        if (param1PlaybackStateCompat == null) {
          param1PlaybackStateCompat = null;
        } else {
          playbackState = (PlaybackState)param1PlaybackStateCompat.j();
        } 
        mediaSession.setPlaybackState(playbackState);
        return;
      } 
    }
    
    public void l(i2.a param1a) {
      synchronized (this.c) {
        this.o = param1a;
        return;
      } 
    }
    
    public i2.a m() {
      synchronized (this.c) {
        return this.o;
      } 
    }
    
    public MediaSession n(Context param1Context, String param1String, Bundle param1Bundle) {
      return new MediaSession(param1Context, param1String);
    }
    
    public void r(int param1Int) {
      if (this.m != param1Int) {
        this.m = param1Int;
        int i = this.f.beginBroadcast() - 1;
        while (true) {
          if (i >= 0) {
            a a1 = (a)this.f.getBroadcastItem(i);
            try {
              a1.D0(param1Int);
            } catch (RemoteException remoteException) {}
            i--;
            continue;
          } 
          this.f.finishBroadcast();
          return;
        } 
      } 
    }
    
    public void release() {
      this.e = true;
      this.f.kill();
      if (Build.VERSION.SDK_INT == 27)
        try {
          Field field = this.a.getClass().getDeclaredField("mCallback");
          field.setAccessible(true);
          Handler handler = (Handler)field.get(this.a);
          if (handler != null)
            handler.removeCallbacksAndMessages(null); 
        } catch (Exception exception) {
          Log.w("MediaSessionCompat", "Exception happened while accessing MediaSession.mCallback.", exception);
        }  
      this.a.setCallback(null);
      this.a.release();
    }
    
    public class a extends b.a {
      public final MediaSessionCompat.d a;
      
      public a(MediaSessionCompat.d this$0) {}
      
      public void B0(int param2Int) {
        throw new AssertionError();
      }
      
      public void C(MediaDescriptionCompat param2MediaDescriptionCompat) {
        throw new AssertionError();
      }
      
      public boolean C0() {
        return this.a.k;
      }
      
      public boolean D() {
        throw new AssertionError();
      }
      
      public void E(MediaDescriptionCompat param2MediaDescriptionCompat) {
        throw new AssertionError();
      }
      
      public void F0(String param2String, Bundle param2Bundle, MediaSessionCompat.ResultReceiverWrapper param2ResultReceiverWrapper) {
        throw new AssertionError();
      }
      
      public PendingIntent G() {
        throw new AssertionError();
      }
      
      public void G0() {
        throw new AssertionError();
      }
      
      public int I() {
        return this.a.j;
      }
      
      public void K(String param2String, Bundle param2Bundle) {
        throw new AssertionError();
      }
      
      public void L0(long param2Long) {
        throw new AssertionError();
      }
      
      public void M0(boolean param2Boolean) {}
      
      public ParcelableVolumeInfo N0() {
        throw new AssertionError();
      }
      
      public String R0() {
        throw new AssertionError();
      }
      
      public CharSequence W() {
        throw new AssertionError();
      }
      
      public void Z(String param2String, Bundle param2Bundle) {
        throw new AssertionError();
      }
      
      public Bundle a0() {
        Bundle bundle;
        if (this.a.d == null) {
          bundle = null;
        } else {
          bundle = new Bundle(this.a.d);
        } 
        return bundle;
      }
      
      public void b0(a param2a) {
        this.a.f.unregister(param2a);
      }
      
      public void c0(String param2String, Bundle param2Bundle) {
        throw new AssertionError();
      }
      
      public void d() {
        throw new AssertionError();
      }
      
      public void d0(String param2String, Bundle param2Bundle) {
        throw new AssertionError();
      }
      
      public void e() {
        throw new AssertionError();
      }
      
      public void f0() {
        throw new AssertionError();
      }
      
      public PlaybackStateCompat g() {
        MediaSessionCompat.d d1 = this.a;
        return MediaSessionCompat.d(d1.g, d1.i);
      }
      
      public void g0(Uri param2Uri, Bundle param2Bundle) {
        throw new AssertionError();
      }
      
      public Bundle getExtras() {
        throw new AssertionError();
      }
      
      public String getTag() {
        throw new AssertionError();
      }
      
      public void h() {
        throw new AssertionError();
      }
      
      public void j(int param2Int) {
        throw new AssertionError();
      }
      
      public MediaMetadataCompat k() {
        throw new AssertionError();
      }
      
      public void l(long param2Long) {
        throw new AssertionError();
      }
      
      public int m() {
        return this.a.l;
      }
      
      public void n(float param2Float) {
        throw new AssertionError();
      }
      
      public boolean n0(KeyEvent param2KeyEvent) {
        throw new AssertionError();
      }
      
      public void next() {
        throw new AssertionError();
      }
      
      public List<MediaSessionCompat.QueueItem> o() {
        return null;
      }
      
      public long p() {
        throw new AssertionError();
      }
      
      public void previous() {
        throw new AssertionError();
      }
      
      public void q0(int param2Int1, int param2Int2, String param2String) {
        throw new AssertionError();
      }
      
      public void r(int param2Int) {
        throw new AssertionError();
      }
      
      public void r0(RatingCompat param2RatingCompat, Bundle param2Bundle) {
        throw new AssertionError();
      }
      
      public void s(String param2String, Bundle param2Bundle) {
        throw new AssertionError();
      }
      
      public void stop() {
        throw new AssertionError();
      }
      
      public void t(a param2a) {
        if (!this.a.e) {
          i2.a a1 = new i2.a("android.media.session.MediaController", Binder.getCallingPid(), Binder.getCallingUid());
          this.a.f.register(param2a, a1);
        } 
      }
      
      public void t0(MediaDescriptionCompat param2MediaDescriptionCompat, int param2Int) {
        throw new AssertionError();
      }
      
      public boolean v() {
        return false;
      }
      
      public void w(RatingCompat param2RatingCompat) {
        throw new AssertionError();
      }
      
      public void w0(boolean param2Boolean) {
        throw new AssertionError();
      }
      
      public void y(int param2Int1, int param2Int2, String param2String) {
        throw new AssertionError();
      }
      
      public void z(Uri param2Uri, Bundle param2Bundle) {
        throw new AssertionError();
      }
      
      public int z0() {
        return this.a.m;
      }
    }
  }
  
  public class a extends b.a {
    public final MediaSessionCompat.d a;
    
    public a(MediaSessionCompat this$0) {}
    
    public void B0(int param1Int) {
      throw new AssertionError();
    }
    
    public void C(MediaDescriptionCompat param1MediaDescriptionCompat) {
      throw new AssertionError();
    }
    
    public boolean C0() {
      return this.a.k;
    }
    
    public boolean D() {
      throw new AssertionError();
    }
    
    public void E(MediaDescriptionCompat param1MediaDescriptionCompat) {
      throw new AssertionError();
    }
    
    public void F0(String param1String, Bundle param1Bundle, MediaSessionCompat.ResultReceiverWrapper param1ResultReceiverWrapper) {
      throw new AssertionError();
    }
    
    public PendingIntent G() {
      throw new AssertionError();
    }
    
    public void G0() {
      throw new AssertionError();
    }
    
    public int I() {
      return this.a.j;
    }
    
    public void K(String param1String, Bundle param1Bundle) {
      throw new AssertionError();
    }
    
    public void L0(long param1Long) {
      throw new AssertionError();
    }
    
    public void M0(boolean param1Boolean) {}
    
    public ParcelableVolumeInfo N0() {
      throw new AssertionError();
    }
    
    public String R0() {
      throw new AssertionError();
    }
    
    public CharSequence W() {
      throw new AssertionError();
    }
    
    public void Z(String param1String, Bundle param1Bundle) {
      throw new AssertionError();
    }
    
    public Bundle a0() {
      Bundle bundle;
      if (this.a.d == null) {
        bundle = null;
      } else {
        bundle = new Bundle(this.a.d);
      } 
      return bundle;
    }
    
    public void b0(a param1a) {
      this.a.f.unregister(param1a);
    }
    
    public void c0(String param1String, Bundle param1Bundle) {
      throw new AssertionError();
    }
    
    public void d() {
      throw new AssertionError();
    }
    
    public void d0(String param1String, Bundle param1Bundle) {
      throw new AssertionError();
    }
    
    public void e() {
      throw new AssertionError();
    }
    
    public void f0() {
      throw new AssertionError();
    }
    
    public PlaybackStateCompat g() {
      MediaSessionCompat.d d1 = this.a;
      return MediaSessionCompat.d(d1.g, d1.i);
    }
    
    public void g0(Uri param1Uri, Bundle param1Bundle) {
      throw new AssertionError();
    }
    
    public Bundle getExtras() {
      throw new AssertionError();
    }
    
    public String getTag() {
      throw new AssertionError();
    }
    
    public void h() {
      throw new AssertionError();
    }
    
    public void j(int param1Int) {
      throw new AssertionError();
    }
    
    public MediaMetadataCompat k() {
      throw new AssertionError();
    }
    
    public void l(long param1Long) {
      throw new AssertionError();
    }
    
    public int m() {
      return this.a.l;
    }
    
    public void n(float param1Float) {
      throw new AssertionError();
    }
    
    public boolean n0(KeyEvent param1KeyEvent) {
      throw new AssertionError();
    }
    
    public void next() {
      throw new AssertionError();
    }
    
    public List<MediaSessionCompat.QueueItem> o() {
      return null;
    }
    
    public long p() {
      throw new AssertionError();
    }
    
    public void previous() {
      throw new AssertionError();
    }
    
    public void q0(int param1Int1, int param1Int2, String param1String) {
      throw new AssertionError();
    }
    
    public void r(int param1Int) {
      throw new AssertionError();
    }
    
    public void r0(RatingCompat param1RatingCompat, Bundle param1Bundle) {
      throw new AssertionError();
    }
    
    public void s(String param1String, Bundle param1Bundle) {
      throw new AssertionError();
    }
    
    public void stop() {
      throw new AssertionError();
    }
    
    public void t(a param1a) {
      if (!this.a.e) {
        i2.a a1 = new i2.a("android.media.session.MediaController", Binder.getCallingPid(), Binder.getCallingUid());
        this.a.f.register(param1a, a1);
      } 
    }
    
    public void t0(MediaDescriptionCompat param1MediaDescriptionCompat, int param1Int) {
      throw new AssertionError();
    }
    
    public boolean v() {
      return false;
    }
    
    public void w(RatingCompat param1RatingCompat) {
      throw new AssertionError();
    }
    
    public void w0(boolean param1Boolean) {
      throw new AssertionError();
    }
    
    public void y(int param1Int1, int param1Int2, String param1String) {
      throw new AssertionError();
    }
    
    public void z(Uri param1Uri, Bundle param1Bundle) {
      throw new AssertionError();
    }
    
    public int z0() {
      return this.a.m;
    }
  }
  
  public static class e extends d {
    public e(Context param1Context, String param1String, m5.d param1d, Bundle param1Bundle) {
      super(param1Context, param1String, param1d, param1Bundle);
    }
  }
  
  public static class f extends e {
    public f(Context param1Context, String param1String, m5.d param1d, Bundle param1Bundle) {
      super(param1Context, param1String, param1d, param1Bundle);
    }
    
    public void l(i2.a param1a) {}
    
    public final i2.a m() {
      return new i2.a(c.a(this.a));
    }
  }
  
  public static class g extends f {
    public g(Context param1Context, String param1String, m5.d param1d, Bundle param1Bundle) {
      super(param1Context, param1String, param1d, param1Bundle);
    }
    
    public MediaSession n(Context param1Context, String param1String, Bundle param1Bundle) {
      return new MediaSession(param1Context, param1String, param1Bundle);
    }
  }
  
  public static interface h {
    void a();
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\android\support\v4\media\session\MediaSessionCompat.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */