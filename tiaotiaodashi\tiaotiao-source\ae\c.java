package ae;

import xg.m;

public final class c {
  public final String a;
  
  public final String b;
  
  public final String c;
  
  public c(String paramString1, String paramString2, String paramString3) {
    this.a = paramString1;
    this.b = paramString2;
    this.c = paramString3;
  }
  
  public final String a() {
    return this.a;
  }
  
  public final String b() {
    return this.b;
  }
  
  public final String c() {
    return this.c;
  }
  
  public boolean equals(Object paramObject) {
    if (this == paramObject)
      return true; 
    if (!(paramObject instanceof c))
      return false; 
    paramObject = paramObject;
    return !m.a(this.a, ((c)paramObject).a) ? false : (!m.a(this.b, ((c)paramObject).b) ? false : (!!m.a(this.c, ((c)paramObject).c)));
  }
  
  public int hashCode() {
    int i;
    int j = this.a.hashCode();
    int k = this.b.hashCode();
    String str = this.c;
    if (str == null) {
      i = 0;
    } else {
      i = str.hashCode();
    } 
    return (j * 31 + k) * 31 + i;
  }
  
  public String toString() {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("NotificationButton(id=");
    stringBuilder.append(this.a);
    stringBuilder.append(", text=");
    stringBuilder.append(this.b);
    stringBuilder.append(", textColorRgb=");
    stringBuilder.append(this.c);
    stringBuilder.append(')');
    return stringBuilder.toString();
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ae\c.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */