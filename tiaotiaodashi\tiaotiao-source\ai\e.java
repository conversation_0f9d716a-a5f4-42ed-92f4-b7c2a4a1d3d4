package ai;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.Arrays;
import java.util.List;
import javax.net.ssl.SSLSocket;
import rh.x;
import xg.g;
import xg.m;

public final class e extends j {
  public static final b i = new b(null);
  
  public final Method d;
  
  public final Method e;
  
  public final Method f;
  
  public final Class<?> g;
  
  public final Class<?> h;
  
  public e(Method paramMethod1, Method paramMethod2, Method paramMethod3, Class<?> paramClass1, Class<?> paramClass2) {
    this.d = paramMethod1;
    this.e = paramMethod2;
    this.f = paramMethod3;
    this.g = paramClass1;
    this.h = paramClass2;
  }
  
  public void b(SSLSocket paramSSLSocket) {
    m.e(paramSSLSocket, "sslSocket");
    try {
      this.f.invoke(null, new Object[] { paramSSLSocket });
      return;
    } catch (IllegalAccessException illegalAccessException) {
      throw new AssertionError("failed to remove ALPN", illegalAccessException);
    } catch (InvocationTargetException invocationTargetException) {
      throw new AssertionError("failed to remove ALPN", invocationTargetException);
    } 
  }
  
  public void e(SSLSocket paramSSLSocket, String paramString, List<? extends x> paramList) {
    m.e(paramSSLSocket, "sslSocket");
    m.e(paramList, "protocols");
    paramList = (List)j.a.b(paramList);
    try {
      ClassLoader classLoader = j.class.getClassLoader();
      Class<?> clazz1 = this.g;
      Class<?> clazz2 = this.h;
      a a = new a();
      this((List)paramList);
      Object object = Proxy.newProxyInstance(classLoader, new Class[] { clazz1, clazz2 }, a);
      this.d.invoke(null, new Object[] { paramSSLSocket, object });
      return;
    } catch (InvocationTargetException invocationTargetException) {
      throw new AssertionError("failed to set ALPN", invocationTargetException);
    } catch (IllegalAccessException illegalAccessException) {
      throw new AssertionError("failed to set ALPN", illegalAccessException);
    } 
  }
  
  public String g(SSLSocket paramSSLSocket) {
    m.e(paramSSLSocket, "sslSocket");
    try {
      String str;
      Method method = this.e;
      InvocationHandler invocationHandler2 = null;
      InvocationHandler invocationHandler1 = Proxy.getInvocationHandler(method.invoke(null, new Object[] { paramSSLSocket }));
      m.c(invocationHandler1, "null cannot be cast to non-null type okhttp3.internal.platform.Jdk8WithJettyBootPlatform.AlpnProvider");
      invocationHandler1 = invocationHandler1;
      if (!invocationHandler1.b() && invocationHandler1.a() == null) {
        j.k(this, "ALPN callback dropped: HTTP/2 is disabled. Is alpn-boot on the boot class path?", 0, null, 6, null);
        return null;
      } 
      if (invocationHandler1.b()) {
        invocationHandler1 = invocationHandler2;
      } else {
        str = invocationHandler1.a();
      } 
      return str;
    } catch (InvocationTargetException invocationTargetException) {
      throw new AssertionError("failed to get ALPN selected protocol", invocationTargetException);
    } catch (IllegalAccessException illegalAccessException) {
      throw new AssertionError("failed to get ALPN selected protocol", illegalAccessException);
    } 
  }
  
  public static final class a implements InvocationHandler {
    public final List<String> a;
    
    public boolean b;
    
    public String c;
    
    public a(List<String> param1List) {
      this.a = param1List;
    }
    
    public final String a() {
      return this.c;
    }
    
    public final boolean b() {
      return this.b;
    }
    
    public Object invoke(Object param1Object, Method param1Method, Object[] param1ArrayOfObject) {
      String str1;
      m.e(param1Object, "proxy");
      m.e(param1Method, "method");
      param1Object = param1ArrayOfObject;
      if (param1ArrayOfObject == null)
        param1Object = new Object[0]; 
      String str2 = param1Method.getName();
      Class<?> clazz = param1Method.getReturnType();
      if (m.a(str2, "supports") && m.a(boolean.class, clazz))
        return Boolean.TRUE; 
      if (m.a(str2, "unsupported") && m.a(void.class, clazz)) {
        this.b = true;
        return null;
      } 
      if (m.a(str2, "protocols")) {
        boolean bool;
        if (param1Object.length == 0) {
          bool = true;
        } else {
          bool = false;
        } 
        if (bool)
          return this.a; 
      } 
      if ((m.a(str2, "selectProtocol") || m.a(str2, "select")) && m.a(String.class, clazz) && param1Object.length == 1) {
        Object object = param1Object[0];
        if (object instanceof List) {
          m.c(object, "null cannot be cast to non-null type kotlin.collections.List<*>");
          param1Object = object;
          int i = param1Object.size();
          if (i >= 0) {
            int j = 0;
            while (true) {
              param1Method = param1Object.get(j);
              m.c(param1Method, "null cannot be cast to non-null type kotlin.String");
              str1 = (String)param1Method;
              if (this.a.contains(str1)) {
                this.c = str1;
                return str1;
              } 
              if (j != i) {
                j++;
                continue;
              } 
              break;
            } 
          } 
          param1Object = this.a.get(0);
          this.c = (String)param1Object;
          return param1Object;
        } 
      } 
      if ((m.a(str2, "protocolSelected") || m.a(str2, "selected")) && param1Object.length == 1) {
        param1Object = param1Object[0];
        m.c(param1Object, "null cannot be cast to non-null type kotlin.String");
        this.c = (String)param1Object;
        return null;
      } 
      return str1.invoke(this, Arrays.copyOf((Object[])param1Object, param1Object.length));
    }
  }
  
  public static final class b {
    public b() {}
    
    public final j a() {
      String str = System.getProperty("java.specification.version", "unknown");
      try {
        m.d(str, "jvmVersion");
        int i = Integer.parseInt(str);
        if (i >= 9)
          return null; 
      } catch (NumberFormatException numberFormatException) {}
      try {
        Class<?> clazz1 = Class.forName("org.eclipse.jetty.alpn.ALPN", true, null);
        StringBuilder stringBuilder1 = new StringBuilder();
        this();
        stringBuilder1.append("org.eclipse.jetty.alpn.ALPN");
        stringBuilder1.append("$Provider");
        Class<?> clazz4 = Class.forName(stringBuilder1.toString(), true, null);
        stringBuilder1 = new StringBuilder();
        this();
        stringBuilder1.append("org.eclipse.jetty.alpn.ALPN");
        stringBuilder1.append("$ClientProvider");
        Class<?> clazz2 = Class.forName(stringBuilder1.toString(), true, null);
        StringBuilder stringBuilder2 = new StringBuilder();
        this();
        stringBuilder2.append("org.eclipse.jetty.alpn.ALPN");
        stringBuilder2.append("$ServerProvider");
        Class<?> clazz3 = Class.forName(stringBuilder2.toString(), true, null);
        Method method3 = clazz1.getMethod("put", new Class[] { SSLSocket.class, clazz4 });
        Method method2 = clazz1.getMethod("get", new Class[] { SSLSocket.class });
        Method method1 = clazz1.getMethod("remove", new Class[] { SSLSocket.class });
        m.d(method3, "putMethod");
        m.d(method2, "getMethod");
        m.d(method1, "removeMethod");
        m.d(clazz2, "clientProviderClass");
        m.d(clazz3, "serverProviderClass");
        return new e(method3, method2, method1, clazz2, clazz3);
      } catch (ClassNotFoundException|NoSuchMethodException classNotFoundException) {
        return null;
      } 
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ai\e.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */