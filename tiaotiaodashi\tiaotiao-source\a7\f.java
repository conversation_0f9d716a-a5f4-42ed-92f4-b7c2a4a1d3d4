package a7;

import java.io.InputStream;
import java.net.URL;
import t6.h;
import z6.h;
import z6.o;
import z6.p;
import z6.s;

public class f implements o<URL, InputStream> {
  public final o<h, InputStream> a;
  
  public f(o<h, InputStream> paramo) {
    this.a = paramo;
  }
  
  public o.a<InputStream> c(URL paramURL, int paramInt1, int paramInt2, h paramh) {
    return this.a.b(new h(paramURL), paramInt1, paramInt2, paramh);
  }
  
  public boolean d(URL paramURL) {
    return true;
  }
  
  public static class a implements p<URL, InputStream> {
    public o<URL, InputStream> c(s param1s) {
      return new f(param1s.d(h.class, InputStream.class));
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a7\f.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */