package a1;

import android.graphics.Insets;
import android.graphics.Rect;
import p.q0;
import p.r0;
import p.s0;
import p.t0;

public final class c {
  public static final c e = new c(0, 0, 0, 0);
  
  public final int a;
  
  public final int b;
  
  public final int c;
  
  public final int d;
  
  public c(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
    this.a = paramInt1;
    this.b = paramInt2;
    this.c = paramInt3;
    this.d = paramInt4;
  }
  
  public static c a(c paramc1, c paramc2) {
    return b(Math.max(paramc1.a, paramc2.a), Math.max(paramc1.b, paramc2.b), Math.max(paramc1.c, paramc2.c), Math.max(paramc1.d, paramc2.d));
  }
  
  public static c b(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
    return (paramInt1 == 0 && paramInt2 == 0 && paramInt3 == 0 && paramInt4 == 0) ? e : new c(paramInt1, paramInt2, paramInt3, paramInt4);
  }
  
  public static c c(Rect paramRect) {
    return b(paramRect.left, paramRect.top, paramRect.right, paramRect.bottom);
  }
  
  public static c d(Insets paramInsets) {
    return b(q0.a(paramInsets), r0.a(paramInsets), s0.a(paramInsets), t0.a(paramInsets));
  }
  
  public Insets e() {
    return a.a(this.a, this.b, this.c, this.d);
  }
  
  public boolean equals(Object paramObject) {
    if (this == paramObject)
      return true; 
    if (paramObject == null || c.class != paramObject.getClass())
      return false; 
    paramObject = paramObject;
    return (this.d != ((c)paramObject).d) ? false : ((this.a != ((c)paramObject).a) ? false : ((this.c != ((c)paramObject).c) ? false : (!(this.b != ((c)paramObject).b))));
  }
  
  public int hashCode() {
    return ((this.a * 31 + this.b) * 31 + this.c) * 31 + this.d;
  }
  
  public String toString() {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("Insets{left=");
    stringBuilder.append(this.a);
    stringBuilder.append(", top=");
    stringBuilder.append(this.b);
    stringBuilder.append(", right=");
    stringBuilder.append(this.c);
    stringBuilder.append(", bottom=");
    stringBuilder.append(this.d);
    stringBuilder.append('}');
    return stringBuilder.toString();
  }
  
  public static class a {
    public static Insets a(int param1Int1, int param1Int2, int param1Int3, int param1Int4) {
      return b.a(param1Int1, param1Int2, param1Int3, param1Int4);
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a1\c.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */