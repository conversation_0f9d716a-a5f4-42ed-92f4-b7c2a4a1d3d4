package a4;

import ec.d;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import l2.w;
import o2.x;
import z3.c;

public final class b extends c {
  public static w c(x paramx) {
    paramx.r(12);
    int j = paramx.h(12);
    int i = paramx.d();
    paramx.r(44);
    paramx.s(paramx.h(12));
    paramx.r(16);
    ArrayList<a> arrayList = new ArrayList();
    while (true) {
      w w;
      int k = paramx.d();
      String str2 = null;
      String str1 = null;
      if (k < i + j - 4) {
        paramx.r(48);
        int m = paramx.h(8);
        paramx.r(4);
        k = paramx.h(12);
        int n = paramx.d() + k;
        for (str2 = null; paramx.d() < n; str2 = str4) {
          String str3;
          String str4;
          k = paramx.h(8);
          int i2 = paramx.h(8);
          int i1 = paramx.d() + i2;
          if (k == 2) {
            k = paramx.h(16);
            paramx.r(8);
            str3 = str1;
            str4 = str2;
            if (k == 3)
              label34: while (true) {
                str3 = str1;
                str4 = str2;
                if (paramx.d() < i1) {
                  str3 = paramx.l(paramx.h(8), d.a);
                  i2 = paramx.h(8);
                  k = 0;
                  while (true) {
                    str1 = str3;
                    if (k < i2) {
                      paramx.s(paramx.h(8));
                      k++;
                      continue;
                    } 
                    continue label34;
                  } 
                } 
                break;
              }  
          } else {
            str3 = str1;
            str4 = str2;
            if (k == 21) {
              str4 = paramx.l(i2, d.a);
              str3 = str1;
            } 
          } 
          paramx.p(i1 * 8);
          str1 = str3;
        } 
        paramx.p(n * 8);
        if (str1 != null && str2 != null) {
          StringBuilder stringBuilder = new StringBuilder();
          stringBuilder.append(str1);
          stringBuilder.append(str2);
          arrayList.add(new a(m, stringBuilder.toString()));
        } 
        continue;
      } 
      if (arrayList.isEmpty()) {
        String str = str2;
      } else {
        w = new w(arrayList);
      } 
      return w;
    } 
  }
  
  public w b(z3.b paramb, ByteBuffer paramByteBuffer) {
    if (paramByteBuffer.get() == 116) {
      w w = c(new x(paramByteBuffer.array(), paramByteBuffer.limit()));
    } else {
      paramb = null;
    } 
    return (w)paramb;
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a4\b.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */