package a1;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Typeface;
import android.graphics.fonts.Font;
import android.graphics.fonts.FontFamily;
import android.graphics.fonts.FontStyle;
import android.os.CancellationSignal;
import g1.g;
import java.io.IOException;
import z0.d;

public class b0 extends c0 {
  public static int i(FontStyle paramFontStyle1, FontStyle paramFontStyle2) {
    byte b;
    int i = Math.abs(q.a(paramFontStyle1) - q.a(paramFontStyle2)) / 100;
    if (r.a(paramFontStyle1) == r.a(paramFontStyle2)) {
      b = 0;
    } else {
      b = 2;
    } 
    return i + b;
  }
  
  public Typeface a(Context paramContext, d.c paramc, Resources paramResources, int paramInt) {
    Exception exception2 = null;
    try {
      d.d[] arrayOfD = paramc.a();
      int i = arrayOfD.length;
      paramContext = null;
      byte b = 0;
      while (true) {
        FontFamily.Builder builder;
        if (b < i) {
          d.d d = arrayOfD[b];
          try {
            boolean bool;
            Font.Builder builder1 = new Font.Builder();
            this(paramResources, d.b());
            builder1 = n.a(builder1, d.e());
            if (d.f()) {
              bool = true;
            } else {
              bool = false;
            } 
            Font font = v.a(u.a(t.a(s.a(builder1, bool), d.c()), d.d()));
            if (paramContext == null) {
              FontFamily.Builder builder2 = new FontFamily.Builder();
              this(font);
              builder = builder2;
            } else {
              w.a(builder, font);
            } 
          } catch (IOException iOException) {}
          b++;
          continue;
        } 
        if (builder == null)
          return null; 
        FontFamily fontFamily = x.a(builder);
        Typeface.CustomFallbackBuilder customFallbackBuilder = new Typeface.CustomFallbackBuilder();
        this(fontFamily);
        return a0.a(z.a(customFallbackBuilder, y.a(h(fontFamily, paramInt))));
      } 
    } catch (Exception exception1) {
      exception1 = exception2;
    } 
    return (Typeface)exception1;
  }
  
  public Typeface b(Context paramContext, CancellationSignal paramCancellationSignal, g.b[] paramArrayOfb, int paramInt) {
    // Byte code:
    //   0: aload_1
    //   1: invokevirtual getContentResolver : ()Landroid/content/ContentResolver;
    //   4: astore #9
    //   6: aload_3
    //   7: arraylength
    //   8: istore #7
    //   10: aconst_null
    //   11: astore_1
    //   12: iconst_0
    //   13: istore #5
    //   15: iload #5
    //   17: iload #7
    //   19: if_icmpge -> 202
    //   22: aload_3
    //   23: iload #5
    //   25: aaload
    //   26: astore #11
    //   28: aload_1
    //   29: astore #8
    //   31: aload #9
    //   33: aload #11
    //   35: invokevirtual d : ()Landroid/net/Uri;
    //   38: ldc 'r'
    //   40: aload_2
    //   41: invokevirtual openFileDescriptor : (Landroid/net/Uri;Ljava/lang/String;Landroid/os/CancellationSignal;)Landroid/os/ParcelFileDescriptor;
    //   44: astore #10
    //   46: aload #10
    //   48: ifnonnull -> 73
    //   51: aload_1
    //   52: astore #8
    //   54: aload #10
    //   56: ifnull -> 193
    //   59: aload_1
    //   60: astore #8
    //   62: aload #10
    //   64: invokevirtual close : ()V
    //   67: aload_1
    //   68: astore #8
    //   70: goto -> 193
    //   73: new android/graphics/fonts/Font$Builder
    //   76: astore #8
    //   78: aload #8
    //   80: aload #10
    //   82: invokespecial <init> : (Landroid/os/ParcelFileDescriptor;)V
    //   85: aload #8
    //   87: aload #11
    //   89: invokevirtual e : ()I
    //   92: invokestatic a : (Landroid/graphics/fonts/Font$Builder;I)Landroid/graphics/fonts/Font$Builder;
    //   95: astore #8
    //   97: aload #11
    //   99: invokevirtual f : ()Z
    //   102: ifeq -> 111
    //   105: iconst_1
    //   106: istore #6
    //   108: goto -> 114
    //   111: iconst_0
    //   112: istore #6
    //   114: aload #8
    //   116: iload #6
    //   118: invokestatic a : (Landroid/graphics/fonts/Font$Builder;I)Landroid/graphics/fonts/Font$Builder;
    //   121: aload #11
    //   123: invokevirtual c : ()I
    //   126: invokestatic a : (Landroid/graphics/fonts/Font$Builder;I)Landroid/graphics/fonts/Font$Builder;
    //   129: invokestatic a : (Landroid/graphics/fonts/Font$Builder;)Landroid/graphics/fonts/Font;
    //   132: astore #8
    //   134: aload_1
    //   135: ifnonnull -> 155
    //   138: new android/graphics/fonts/FontFamily$Builder
    //   141: dup
    //   142: aload #8
    //   144: invokespecial <init> : (Landroid/graphics/fonts/Font;)V
    //   147: astore #8
    //   149: aload #8
    //   151: astore_1
    //   152: goto -> 59
    //   155: aload_1
    //   156: aload #8
    //   158: invokestatic a : (Landroid/graphics/fonts/FontFamily$Builder;Landroid/graphics/fonts/Font;)Landroid/graphics/fonts/FontFamily$Builder;
    //   161: pop
    //   162: goto -> 59
    //   165: astore #11
    //   167: aload #10
    //   169: invokevirtual close : ()V
    //   172: goto -> 187
    //   175: astore #10
    //   177: aload_1
    //   178: astore #8
    //   180: aload #11
    //   182: aload #10
    //   184: invokevirtual addSuppressed : (Ljava/lang/Throwable;)V
    //   187: aload_1
    //   188: astore #8
    //   190: aload #11
    //   192: athrow
    //   193: iinc #5, 1
    //   196: aload #8
    //   198: astore_1
    //   199: goto -> 15
    //   202: aload_1
    //   203: ifnonnull -> 208
    //   206: aconst_null
    //   207: areturn
    //   208: aload_1
    //   209: invokestatic a : (Landroid/graphics/fonts/FontFamily$Builder;)Landroid/graphics/fonts/FontFamily;
    //   212: astore_2
    //   213: new android/graphics/Typeface$CustomFallbackBuilder
    //   216: astore_1
    //   217: aload_1
    //   218: aload_2
    //   219: invokespecial <init> : (Landroid/graphics/fonts/FontFamily;)V
    //   222: aload_1
    //   223: aload_0
    //   224: aload_2
    //   225: iload #4
    //   227: invokevirtual h : (Landroid/graphics/fonts/FontFamily;I)Landroid/graphics/fonts/Font;
    //   230: invokestatic a : (Landroid/graphics/fonts/Font;)Landroid/graphics/fonts/FontStyle;
    //   233: invokestatic a : (Landroid/graphics/Typeface$CustomFallbackBuilder;Landroid/graphics/fonts/FontStyle;)Landroid/graphics/Typeface$CustomFallbackBuilder;
    //   236: invokestatic a : (Landroid/graphics/Typeface$CustomFallbackBuilder;)Landroid/graphics/Typeface;
    //   239: astore_1
    //   240: aload_1
    //   241: areturn
    //   242: astore_1
    //   243: aconst_null
    //   244: areturn
    //   245: astore_1
    //   246: goto -> 193
    // Exception table:
    //   from	to	target	type
    //   6	10	242	java/lang/Exception
    //   31	46	245	java/io/IOException
    //   31	46	242	java/lang/Exception
    //   62	67	245	java/io/IOException
    //   62	67	242	java/lang/Exception
    //   73	105	165	finally
    //   114	134	165	finally
    //   138	149	165	finally
    //   155	162	165	finally
    //   167	172	175	finally
    //   180	187	245	java/io/IOException
    //   180	187	242	java/lang/Exception
    //   190	193	245	java/io/IOException
    //   190	193	242	java/lang/Exception
    //   208	240	242	java/lang/Exception
  }
  
  public Typeface d(Context paramContext, Resources paramResources, int paramInt1, String paramString, int paramInt2) {
    try {
      Font.Builder builder = new Font.Builder();
      this(paramResources, paramInt1);
      Font font = v.a(builder);
      FontFamily.Builder builder1 = new FontFamily.Builder();
      this(font);
      FontFamily fontFamily = x.a(builder1);
      Typeface.CustomFallbackBuilder customFallbackBuilder = new Typeface.CustomFallbackBuilder();
      this(fontFamily);
      return a0.a(z.a(customFallbackBuilder, y.a(font)));
    } catch (Exception exception) {
      return null;
    } 
  }
  
  public g.b g(g.b[] paramArrayOfb, int paramInt) {
    throw new RuntimeException("Do not use this function in API 29 or later.");
  }
  
  public final Font h(FontFamily paramFontFamily, int paramInt) {
    if ((paramInt & 0x1) != 0) {
      i = 700;
    } else {
      i = 400;
    } 
    int j = 1;
    if ((paramInt & 0x2) != 0) {
      paramInt = 1;
    } else {
      paramInt = 0;
    } 
    FontStyle fontStyle = new FontStyle(i, paramInt);
    Font font = o.a(paramFontFamily, 0);
    int i = i(fontStyle, y.a(font));
    paramInt = j;
    while (paramInt < p.a(paramFontFamily)) {
      Font font1 = o.a(paramFontFamily, paramInt);
      int k = i(fontStyle, y.a(font1));
      j = i;
      if (k < i) {
        font = font1;
        j = k;
      } 
      paramInt++;
      i = j;
    } 
    return font;
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a1\b0.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */