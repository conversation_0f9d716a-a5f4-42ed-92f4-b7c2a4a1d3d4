package a5;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.zip.DataFormatException;
import java.util.zip.Deflater;
import java.util.zip.DeflaterOutputStream;
import java.util.zip.Inflater;

public class c {
  public static int a(int paramInt) {
    return (paramInt + 8 - 1 & 0xFFFFFFF8) / 8;
  }
  
  public static byte[] b(byte[] paramArrayOfbyte) {
    Deflater deflater = new Deflater(1);
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    try {
      DeflaterOutputStream deflaterOutputStream = new DeflaterOutputStream();
      this(byteArrayOutputStream, deflater);
    } finally {
      deflater.end();
    } 
  }
  
  public static RuntimeException c(String paramString) {
    return new IllegalStateException(paramString);
  }
  
  public static byte[] d(InputStream paramInputStream, int paramInt) {
    byte[] arrayOfByte = new byte[paramInt];
    int i = 0;
    while (i < paramInt) {
      int j = paramInputStream.read(arrayOfByte, i, paramInt - i);
      if (j >= 0) {
        i += j;
        continue;
      } 
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("Not enough bytes to read: ");
      stringBuilder.append(paramInt);
      throw c(stringBuilder.toString());
    } 
    return arrayOfByte;
  }
  
  public static byte[] e(InputStream paramInputStream, int paramInt1, int paramInt2) {
    Inflater inflater = new Inflater();
    try {
      byte[] arrayOfByte2 = new byte[paramInt2];
      byte[] arrayOfByte1 = new byte[2048];
      int i = 0;
      int j = 0;
      while (!inflater.finished() && !inflater.needsDictionary() && i < paramInt1) {
        int k = paramInputStream.read(arrayOfByte1);
        if (k >= 0) {
          inflater.setInput(arrayOfByte1, 0, k);
          try {
            int m = inflater.inflate(arrayOfByte2, j, paramInt2 - j);
            j += m;
            i += k;
          } catch (DataFormatException dataFormatException) {
            throw c(dataFormatException.getMessage());
          } 
          continue;
        } 
        StringBuilder stringBuilder1 = new StringBuilder();
        this();
        stringBuilder1.append("Invalid zip data. Stream ended after $totalBytesRead bytes. Expected ");
        stringBuilder1.append(paramInt1);
        stringBuilder1.append(" bytes");
        throw c(stringBuilder1.toString());
      } 
      if (i == paramInt1) {
        boolean bool = inflater.finished();
        if (bool)
          return arrayOfByte2; 
        throw c("Inflater did not finish");
      } 
      StringBuilder stringBuilder = new StringBuilder();
      this();
      stringBuilder.append("Didn't read enough bytes during decompression. expected=");
      stringBuilder.append(paramInt1);
      stringBuilder.append(" actual=");
      stringBuilder.append(i);
      throw c(stringBuilder.toString());
    } finally {
      inflater.end();
    } 
  }
  
  public static String f(InputStream paramInputStream, int paramInt) {
    return new String(d(paramInputStream, paramInt), StandardCharsets.UTF_8);
  }
  
  public static long g(InputStream paramInputStream, int paramInt) {
    byte[] arrayOfByte = d(paramInputStream, paramInt);
    long l = 0L;
    for (byte b = 0; b < paramInt; b++)
      l += (arrayOfByte[b] & 0xFF) << b * 8; 
    return l;
  }
  
  public static int h(InputStream paramInputStream) {
    return (int)g(paramInputStream, 2);
  }
  
  public static long i(InputStream paramInputStream) {
    return g(paramInputStream, 4);
  }
  
  public static int j(InputStream paramInputStream) {
    return (int)g(paramInputStream, 1);
  }
  
  public static int k(String paramString) {
    return (paramString.getBytes(StandardCharsets.UTF_8)).length;
  }
  
  public static void l(InputStream paramInputStream, OutputStream paramOutputStream) {
    byte[] arrayOfByte = new byte[512];
    while (true) {
      int i = paramInputStream.read(arrayOfByte);
      if (i > 0) {
        paramOutputStream.write(arrayOfByte, 0, i);
        continue;
      } 
      break;
    } 
  }
  
  public static void m(OutputStream paramOutputStream, byte[] paramArrayOfbyte) {
    q(paramOutputStream, paramArrayOfbyte.length);
    paramArrayOfbyte = b(paramArrayOfbyte);
    q(paramOutputStream, paramArrayOfbyte.length);
    paramOutputStream.write(paramArrayOfbyte);
  }
  
  public static void n(OutputStream paramOutputStream, String paramString) {
    paramOutputStream.write(paramString.getBytes(StandardCharsets.UTF_8));
  }
  
  public static void o(OutputStream paramOutputStream, long paramLong, int paramInt) {
    byte[] arrayOfByte = new byte[paramInt];
    for (byte b = 0; b < paramInt; b++)
      arrayOfByte[b] = (byte)(int)(paramLong >> b * 8 & 0xFFL); 
    paramOutputStream.write(arrayOfByte);
  }
  
  public static void p(OutputStream paramOutputStream, int paramInt) {
    o(paramOutputStream, paramInt, 2);
  }
  
  public static void q(OutputStream paramOutputStream, long paramLong) {
    o(paramOutputStream, paramLong, 4);
  }
  
  public static void r(OutputStream paramOutputStream, int paramInt) {
    o(paramOutputStream, paramInt, 1);
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a5\c.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */