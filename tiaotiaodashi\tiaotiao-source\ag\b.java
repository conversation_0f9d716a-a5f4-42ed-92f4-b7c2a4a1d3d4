package ag;

import android.graphics.Rect;
import d1.a;

public final class b {
  public static Float a(float paramFloat1, float paramFloat2, float paramFloat3) {
    return Float.valueOf(a.b(paramFloat1, paramFloat2, paramFloat3));
  }
  
  public static Rect b(float paramFloat1, Rect paramRect, float paramFloat2, float paramFloat3) {
    paramFloat1 = a(paramFloat1, paramFloat2, paramFloat3).floatValue();
    int k = paramRect.width() / 2;
    int j = paramRect.height() / 2;
    int m = (int)(paramRect.width() * 0.5F / paramFloat1);
    int i = (int)(paramRect.height() * 0.5F / paramFloat1);
    return new Rect(k - m, j - i, k + m, j + i);
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ag\b.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */