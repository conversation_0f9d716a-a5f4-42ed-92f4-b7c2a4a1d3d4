package ai;

import bi.e;
import di.b;
import di.c;
import di.e;
import ei.d;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.security.GeneralSecurityException;
import java.security.KeyStore;
import java.security.Security;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import lg.m;
import rh.w;
import rh.x;
import xg.g;
import xg.m;

public class j {
  public static final a a;
  
  public static volatile j b;
  
  public static final Logger c = Logger.getLogger(w.class.getName());
  
  public void b(SSLSocket paramSSLSocket) {
    m.e(paramSSLSocket, "sslSocket");
  }
  
  public c c(X509TrustManager paramX509TrustManager) {
    m.e(paramX509TrustManager, "trustManager");
    return (c)new di.a(d(paramX509TrustManager));
  }
  
  public e d(X509TrustManager paramX509TrustManager) {
    m.e(paramX509TrustManager, "trustManager");
    X509Certificate[] arrayOfX509Certificate = paramX509TrustManager.getAcceptedIssuers();
    m.d(arrayOfX509Certificate, "trustManager.acceptedIssuers");
    return (e)new b(Arrays.<X509Certificate>copyOf(arrayOfX509Certificate, arrayOfX509Certificate.length));
  }
  
  public void e(SSLSocket paramSSLSocket, String paramString, List<x> paramList) {
    m.e(paramSSLSocket, "sslSocket");
    m.e(paramList, "protocols");
  }
  
  public void f(Socket paramSocket, InetSocketAddress paramInetSocketAddress, int paramInt) {
    m.e(paramSocket, "socket");
    m.e(paramInetSocketAddress, "address");
    paramSocket.connect(paramInetSocketAddress, paramInt);
  }
  
  public String g(SSLSocket paramSSLSocket) {
    m.e(paramSSLSocket, "sslSocket");
    return null;
  }
  
  public Object h(String paramString) {
    m.e(paramString, "closer");
    if (c.isLoggable(Level.FINE)) {
      Throwable throwable = new Throwable(paramString);
    } else {
      paramString = null;
    } 
    return paramString;
  }
  
  public boolean i(String paramString) {
    m.e(paramString, "hostname");
    return true;
  }
  
  public void j(String paramString, int paramInt, Throwable paramThrowable) {
    Level level;
    m.e(paramString, "message");
    if (paramInt == 5) {
      level = Level.WARNING;
    } else {
      level = Level.INFO;
    } 
    c.log(level, paramString, paramThrowable);
  }
  
  public void l(String paramString, Object paramObject) {
    m.e(paramString, "message");
    String str = paramString;
    if (paramObject == null) {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append(paramString);
      stringBuilder.append(" To see where this was allocated, set the OkHttpClient logger level to FINE: Logger.getLogger(OkHttpClient.class.getName()).setLevel(Level.FINE);");
      str = stringBuilder.toString();
    } 
    j(str, 5, (Throwable)paramObject);
  }
  
  public SSLContext m() {
    SSLContext sSLContext = SSLContext.getInstance("TLS");
    m.d(sSLContext, "getInstance(\"TLS\")");
    return sSLContext;
  }
  
  public SSLSocketFactory n(X509TrustManager paramX509TrustManager) {
    m.e(paramX509TrustManager, "trustManager");
    try {
      SSLContext sSLContext = m();
      sSLContext.init(null, new TrustManager[] { paramX509TrustManager }, null);
      SSLSocketFactory sSLSocketFactory = sSLContext.getSocketFactory();
      m.d(sSLSocketFactory, "newSSLContext().apply {\n…ll)\n      }.socketFactory");
      return sSLSocketFactory;
    } catch (GeneralSecurityException generalSecurityException) {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("No System TLS: ");
      stringBuilder.append(generalSecurityException);
      throw new AssertionError(stringBuilder.toString(), generalSecurityException);
    } 
  }
  
  public X509TrustManager o() {
    TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
    trustManagerFactory.init((KeyStore)null);
    TrustManager[] arrayOfTrustManager = trustManagerFactory.getTrustManagers();
    m.b(arrayOfTrustManager);
    int i = arrayOfTrustManager.length;
    boolean bool = true;
    if (i != 1 || !(arrayOfTrustManager[0] instanceof X509TrustManager))
      bool = false; 
    if (bool) {
      TrustManager trustManager = arrayOfTrustManager[0];
      m.c(trustManager, "null cannot be cast to non-null type javax.net.ssl.X509TrustManager");
      return (X509TrustManager)trustManager;
    } 
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("Unexpected default trust managers: ");
    String str = Arrays.toString((Object[])arrayOfTrustManager);
    m.d(str, "toString(this)");
    stringBuilder.append(str);
    throw new IllegalStateException(stringBuilder.toString().toString());
  }
  
  public String toString() {
    String str = getClass().getSimpleName();
    m.d(str, "javaClass.simpleName");
    return str;
  }
  
  static {
    a a1 = new a(null);
    a = a1;
    b = a.a(a1);
  }
  
  public static final class a {
    public a() {}
    
    public final List<String> b(List<? extends x> param1List) {
      m.e(param1List, "protocols");
      List<? extends x> list = param1List;
      param1List = new ArrayList<x>();
      for (x x : list) {
        boolean bool;
        if ((x)x != x.HTTP_1_0) {
          bool = true;
        } else {
          bool = false;
        } 
        if (bool)
          param1List.add(x); 
      } 
      list = new ArrayList<x>(m.q(param1List, 10));
      Iterator<? extends x> iterator = param1List.iterator();
      while (iterator.hasNext())
        list.add(((x)iterator.next()).toString()); 
      return (List)list;
    }
    
    public final byte[] c(List<? extends x> param1List) {
      m.e(param1List, "protocols");
      d d = new d();
      for (String str : b(param1List)) {
        d.L0(str.length());
        d.Q0(str);
      } 
      return d.y();
    }
    
    public final j d() {
      e.a.b();
      j j2 = a.e.a();
      j j1 = j2;
      if (j2 == null) {
        j1 = b.f.a();
        m.b(j1);
      } 
      return j1;
    }
    
    public final j e() {
      if (j()) {
        d d = d.e.b();
        if (d != null)
          return d; 
      } 
      if (i()) {
        c c = c.e.a();
        if (c != null)
          return c; 
      } 
      if (k()) {
        i i = i.e.a();
        if (i != null)
          return i; 
      } 
      h h = h.d.a();
      if (h != null)
        return h; 
      j j = e.i.a();
      return (j != null) ? j : new j();
    }
    
    public final j f() {
      j j;
      if (h()) {
        j = d();
      } else {
        j = e();
      } 
      return j;
    }
    
    public final j g() {
      return j.a();
    }
    
    public final boolean h() {
      return m.a("Dalvik", System.getProperty("java.vm.name"));
    }
    
    public final boolean i() {
      return m.a("BC", Security.getProviders()[0].getName());
    }
    
    public final boolean j() {
      return m.a("Conscrypt", Security.getProviders()[0].getName());
    }
    
    public final boolean k() {
      return m.a("OpenJSSE", Security.getProviders()[0].getName());
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ai\j.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */