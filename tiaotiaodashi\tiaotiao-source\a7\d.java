package a7;

import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.ParcelFileDescriptor;
import android.text.TextUtils;
import com.bumptech.glide.g;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.InputStream;
import q6.r;
import t6.f;
import t6.h;
import z6.o;
import z6.p;
import z6.s;

public final class d<DataT> implements o<Uri, DataT> {
  public final Context a;
  
  public final o<File, DataT> b;
  
  public final o<Uri, DataT> c;
  
  public final Class<DataT> d;
  
  public d(Context paramContext, o<File, DataT> paramo, o<Uri, DataT> paramo1, Class<DataT> paramClass) {
    this.a = paramContext.getApplicationContext();
    this.b = paramo;
    this.c = paramo1;
    this.d = paramClass;
  }
  
  public o.a<DataT> c(Uri paramUri, int paramInt1, int paramInt2, h paramh) {
    return new o.a((f)new o7.d(paramUri), new d<DataT>(this.a, this.b, this.c, paramUri, paramInt1, paramInt2, paramh, this.d));
  }
  
  public boolean d(Uri paramUri) {
    boolean bool;
    if (Build.VERSION.SDK_INT >= 29 && u6.b.b(paramUri)) {
      bool = true;
    } else {
      bool = false;
    } 
    return bool;
  }
  
  public static abstract class a<DataT> implements p<Uri, DataT> {
    public final Context a;
    
    public final Class<DataT> b;
    
    public a(Context param1Context, Class<DataT> param1Class) {
      this.a = param1Context;
      this.b = param1Class;
    }
    
    public final o<Uri, DataT> c(s param1s) {
      return new d<DataT>(this.a, param1s.d(File.class, this.b), param1s.d(Uri.class, this.b), this.b);
    }
  }
  
  public static final class b extends a<ParcelFileDescriptor> {
    public b(Context param1Context) {
      super(param1Context, ParcelFileDescriptor.class);
    }
  }
  
  public static final class c extends a<InputStream> {
    public c(Context param1Context) {
      super(param1Context, InputStream.class);
    }
  }
  
  public static final class d<DataT> implements com.bumptech.glide.load.data.d<DataT> {
    public static final String[] k = new String[] { "_data" };
    
    public final Context a;
    
    public final o<File, DataT> b;
    
    public final o<Uri, DataT> c;
    
    public final Uri d;
    
    public final int e;
    
    public final int f;
    
    public final h g;
    
    public final Class<DataT> h;
    
    public volatile boolean i;
    
    public volatile com.bumptech.glide.load.data.d<DataT> j;
    
    public d(Context param1Context, o<File, DataT> param1o, o<Uri, DataT> param1o1, Uri param1Uri, int param1Int1, int param1Int2, h param1h, Class<DataT> param1Class) {
      this.a = param1Context.getApplicationContext();
      this.b = param1o;
      this.c = param1o1;
      this.d = param1Uri;
      this.e = param1Int1;
      this.f = param1Int2;
      this.g = param1h;
      this.h = param1Class;
    }
    
    public Class<DataT> a() {
      return this.h;
    }
    
    public void b() {
      com.bumptech.glide.load.data.d<DataT> d1 = this.j;
      if (d1 != null)
        d1.b(); 
    }
    
    public final o.a<DataT> c() {
      Uri uri;
      if (r.a())
        return this.b.b(h(this.d), this.e, this.f, this.g); 
      if (g()) {
        uri = e.a(this.d);
      } else {
        uri = this.d;
      } 
      return this.c.b(uri, this.e, this.f, this.g);
    }
    
    public void cancel() {
      this.i = true;
      com.bumptech.glide.load.data.d<DataT> d1 = this.j;
      if (d1 != null)
        d1.cancel(); 
    }
    
    public t6.a d() {
      return t6.a.LOCAL;
    }
    
    public void e(g param1g, com.bumptech.glide.load.data.d.a<? super DataT> param1a) {
      try {
        IllegalArgumentException illegalArgumentException;
        StringBuilder stringBuilder;
        com.bumptech.glide.load.data.d<DataT> d1 = f();
        if (d1 == null) {
          illegalArgumentException = new IllegalArgumentException();
          stringBuilder = new StringBuilder();
          this();
          stringBuilder.append("Failed to build fetcher for: ");
          stringBuilder.append(this.d);
          this(stringBuilder.toString());
          param1a.c(illegalArgumentException);
          return;
        } 
        this.j = (com.bumptech.glide.load.data.d<DataT>)stringBuilder;
        if (this.i) {
          cancel();
        } else {
          stringBuilder.e((g)illegalArgumentException, param1a);
        } 
      } catch (FileNotFoundException fileNotFoundException) {
        param1a.c(fileNotFoundException);
      } 
    }
    
    public final com.bumptech.glide.load.data.d<DataT> f() {
      o.a<DataT> a = c();
      if (a != null) {
        com.bumptech.glide.load.data.d d1 = a.c;
      } else {
        a = null;
      } 
      return (com.bumptech.glide.load.data.d<DataT>)a;
    }
    
    public final boolean g() {
      boolean bool;
      if (this.a.checkSelfPermission("android.permission.ACCESS_MEDIA_LOCATION") == 0) {
        bool = true;
      } else {
        bool = false;
      } 
      return bool;
    }
    
    public final File h(Uri param1Uri) {
      Cursor cursor = null;
      try {
        File file;
        Cursor cursor1 = this.a.getContentResolver().query(param1Uri, k, null, null, null);
        if (cursor1 != null) {
          cursor = cursor1;
          if (cursor1.moveToFirst()) {
            cursor = cursor1;
            String str = cursor1.getString(cursor1.getColumnIndexOrThrow("_data"));
            cursor = cursor1;
            if (!TextUtils.isEmpty(str)) {
              cursor = cursor1;
              file = new File(str);
              return file;
            } 
            cursor = cursor1;
            FileNotFoundException fileNotFoundException1 = new FileNotFoundException();
            cursor = cursor1;
            StringBuilder stringBuilder1 = new StringBuilder();
            cursor = cursor1;
            this();
            cursor = cursor1;
            stringBuilder1.append("File path was empty in media store for: ");
            cursor = cursor1;
            stringBuilder1.append(file);
            cursor = cursor1;
            this(stringBuilder1.toString());
            cursor = cursor1;
            throw fileNotFoundException1;
          } 
        } 
        cursor = cursor1;
        FileNotFoundException fileNotFoundException = new FileNotFoundException();
        cursor = cursor1;
        StringBuilder stringBuilder = new StringBuilder();
        cursor = cursor1;
        this();
        cursor = cursor1;
        stringBuilder.append("Failed to media store entry for: ");
        cursor = cursor1;
        stringBuilder.append(file);
        cursor = cursor1;
        this(stringBuilder.toString());
        cursor = cursor1;
        throw fileNotFoundException;
      } finally {
        if (cursor != null)
          cursor.close(); 
      } 
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a7\d.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */