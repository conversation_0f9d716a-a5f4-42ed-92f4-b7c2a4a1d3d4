package ae;

import android.content.Context;
import android.content.SharedPreferences;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.json.JSONArray;
import org.json.JSONObject;
import xg.g;
import xg.m;

public final class e {
  public static final a p = new a(null);
  
  public final int a;
  
  public final String b;
  
  public final String c;
  
  public final String d;
  
  public final int e;
  
  public final int f;
  
  public final String g;
  
  public final String h;
  
  public final boolean i;
  
  public final boolean j;
  
  public final boolean k;
  
  public final boolean l;
  
  public final int m;
  
  public final d n;
  
  public final List<c> o;
  
  public e(int paramInt1, String paramString1, String paramString2, String paramString3, int paramInt2, int paramInt3, String paramString4, String paramString5, boolean paramBoolean1, boolean paramBoolean2, boolean paramBoolean3, boolean paramBoolean4, int paramInt4, d paramd, List<c> paramList) {
    this.a = paramInt1;
    this.b = paramString1;
    this.c = paramString2;
    this.d = paramString3;
    this.e = paramInt2;
    this.f = paramInt3;
    this.g = paramString4;
    this.h = paramString5;
    this.i = paramBoolean1;
    this.j = paramBoolean2;
    this.k = paramBoolean3;
    this.l = paramBoolean4;
    this.m = paramInt4;
    this.n = paramd;
    this.o = paramList;
  }
  
  public final List<c> a() {
    return this.o;
  }
  
  public final String b() {
    return this.d;
  }
  
  public final String c() {
    return this.b;
  }
  
  public final int d() {
    return this.e;
  }
  
  public final String e() {
    return this.c;
  }
  
  public boolean equals(Object paramObject) {
    if (this == paramObject)
      return true; 
    if (!(paramObject instanceof e))
      return false; 
    paramObject = paramObject;
    return (this.a != ((e)paramObject).a) ? false : (!m.a(this.b, ((e)paramObject).b) ? false : (!m.a(this.c, ((e)paramObject).c) ? false : (!m.a(this.d, ((e)paramObject).d) ? false : ((this.e != ((e)paramObject).e) ? false : ((this.f != ((e)paramObject).f) ? false : (!m.a(this.g, ((e)paramObject).g) ? false : (!m.a(this.h, ((e)paramObject).h) ? false : ((this.i != ((e)paramObject).i) ? false : ((this.j != ((e)paramObject).j) ? false : ((this.k != ((e)paramObject).k) ? false : ((this.l != ((e)paramObject).l) ? false : ((this.m != ((e)paramObject).m) ? false : (!m.a(this.n, ((e)paramObject).n) ? false : (!!m.a(this.o, ((e)paramObject).o)))))))))))))));
  }
  
  public final String f() {
    return this.h;
  }
  
  public final String g() {
    return this.g;
  }
  
  public final boolean h() {
    return this.i;
  }
  
  public int hashCode() {
    int i;
    int k = Integer.hashCode(this.a);
    int m = this.b.hashCode();
    int n = this.c.hashCode();
    String str = this.d;
    int j = 0;
    if (str == null) {
      i = 0;
    } else {
      i = str.hashCode();
    } 
    int i3 = Integer.hashCode(this.e);
    int i5 = Integer.hashCode(this.f);
    int i6 = this.g.hashCode();
    int i1 = this.h.hashCode();
    int i9 = Boolean.hashCode(this.i);
    int i8 = Boolean.hashCode(this.j);
    int i7 = Boolean.hashCode(this.k);
    int i4 = Boolean.hashCode(this.l);
    int i2 = Integer.hashCode(this.m);
    d d1 = this.n;
    if (d1 != null)
      j = d1.hashCode(); 
    return (((((((((((((k * 31 + m) * 31 + n) * 31 + i) * 31 + i3) * 31 + i5) * 31 + i6) * 31 + i1) * 31 + i9) * 31 + i8) * 31 + i7) * 31 + i4) * 31 + i2) * 31 + j) * 31 + this.o.hashCode();
  }
  
  public final d i() {
    return this.n;
  }
  
  public final int j() {
    return this.a;
  }
  
  public final boolean k() {
    return this.j;
  }
  
  public final int l() {
    return this.f;
  }
  
  public final boolean m() {
    return this.k;
  }
  
  public final int n() {
    return this.m;
  }
  
  public final boolean o() {
    return this.l;
  }
  
  public String toString() {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("NotificationOptions(id=");
    stringBuilder.append(this.a);
    stringBuilder.append(", channelId=");
    stringBuilder.append(this.b);
    stringBuilder.append(", channelName=");
    stringBuilder.append(this.c);
    stringBuilder.append(", channelDescription=");
    stringBuilder.append(this.d);
    stringBuilder.append(", channelImportance=");
    stringBuilder.append(this.e);
    stringBuilder.append(", priority=");
    stringBuilder.append(this.f);
    stringBuilder.append(", contentTitle=");
    stringBuilder.append(this.g);
    stringBuilder.append(", contentText=");
    stringBuilder.append(this.h);
    stringBuilder.append(", enableVibration=");
    stringBuilder.append(this.i);
    stringBuilder.append(", playSound=");
    stringBuilder.append(this.j);
    stringBuilder.append(", showWhen=");
    stringBuilder.append(this.k);
    stringBuilder.append(", isSticky=");
    stringBuilder.append(this.l);
    stringBuilder.append(", visibility=");
    stringBuilder.append(this.m);
    stringBuilder.append(", iconData=");
    stringBuilder.append(this.n);
    stringBuilder.append(", buttons=");
    stringBuilder.append(this.o);
    stringBuilder.append(')');
    return stringBuilder.toString();
  }
  
  public static final class a {
    public a() {}
    
    public final void a(Context param1Context) {
      m.e(param1Context, "context");
      SharedPreferences.Editor editor = param1Context.getSharedPreferences("com.pravera.flutter_foreground_task.prefs.NOTIFICATION_OPTIONS", 0).edit();
      editor.clear();
      editor.commit();
    }
    
    public final e b(Context param1Context) {
      m.e(param1Context, "context");
      SharedPreferences sharedPreferences = param1Context.getSharedPreferences("com.pravera.flutter_foreground_task.prefs.NOTIFICATION_OPTIONS", 0);
      int i = sharedPreferences.getInt("notificationId", 1000);
      String str1 = sharedPreferences.getString("notificationChannelId", null);
      String str2 = str1;
      if (str1 == null)
        str2 = "foreground_service"; 
      str1 = sharedPreferences.getString("notificationChannelName", null);
      String str3 = str1;
      if (str1 == null)
        str3 = "Foreground Service"; 
      String str8 = sharedPreferences.getString("notificationChannelDescription", null);
      int m = sharedPreferences.getInt("notificationChannelImportance", 3);
      int j = sharedPreferences.getInt("notificationPriority", 0);
      String str4 = sharedPreferences.getString("notificationContentTitle", null);
      if (str4 == null)
        str4 = ""; 
      String str5 = sharedPreferences.getString("notificationContentText", null);
      if (str5 == null)
        str5 = ""; 
      boolean bool2 = sharedPreferences.getBoolean("enableVibration", false);
      boolean bool1 = sharedPreferences.getBoolean("playSound", false);
      boolean bool3 = sharedPreferences.getBoolean("showWhen", false);
      boolean bool4 = sharedPreferences.getBoolean("isSticky", true);
      int k = sharedPreferences.getInt("visibility", 1);
      str1 = sharedPreferences.getString("iconData", null);
      if (str1 != null) {
        JSONObject jSONObject = new JSONObject(str1);
        String str9 = jSONObject.getString("resType");
        str1 = str9;
        if (str9 == null)
          str1 = ""; 
        String str10 = jSONObject.getString("resPrefix");
        str9 = str10;
        if (str10 == null)
          str9 = ""; 
        String str11 = jSONObject.getString("name");
        str10 = str11;
        if (str11 == null)
          str10 = ""; 
        d d = new d(str1, str9, str10, jSONObject.getString("backgroundColorRgb"));
      } else {
        str1 = null;
      } 
      String str6 = sharedPreferences.getString("buttons", null);
      ArrayList<c> arrayList = new ArrayList();
      String str7 = str1;
      if (str6 != null) {
        JSONArray jSONArray = new JSONArray(str6);
        int n = jSONArray.length();
        byte b = 0;
        while (true) {
          str7 = str1;
          if (b < n) {
            JSONObject jSONObject = jSONArray.getJSONObject(b);
            String str9 = jSONObject.getString("id");
            str7 = str9;
            if (str9 == null)
              str7 = ""; 
            String str10 = jSONObject.getString("text");
            str9 = str10;
            if (str10 == null)
              str9 = ""; 
            arrayList.add(new c(str7, str9, jSONObject.getString("textColorRgb")));
            b++;
            continue;
          } 
          break;
        } 
      } 
      return new e(i, str2, str3, str8, m, j, str4, str5, bool2, bool1, bool3, bool4, k, (d)str7, arrayList);
    }
    
    public final void c(Context param1Context, Map<?, ?> param1Map) {
      char c;
      byte b;
      boolean bool1;
      boolean bool2;
      boolean bool3;
      boolean bool4;
      boolean bool5;
      boolean bool6;
      String str1;
      String str2;
      String str3;
      Context context1;
      String str4;
      m.e(param1Context, "context");
      SharedPreferences sharedPreferences = param1Context.getSharedPreferences("com.pravera.flutter_foreground_task.prefs.NOTIFICATION_OPTIONS", 0);
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("notificationId");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof Integer) {
        Integer integer = (Integer)param1Context;
      } else {
        param1Context = null;
      } 
      if (param1Context != null) {
        c = param1Context.intValue();
      } else {
        c = 'Ϩ';
      } 
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("notificationChannelId");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof String) {
        str1 = (String)param1Context;
      } else {
        str1 = null;
      } 
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("notificationChannelName");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof String) {
        str2 = (String)param1Context;
      } else {
        str2 = null;
      } 
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("notificationChannelDescription");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof String) {
        str3 = (String)param1Context;
      } else {
        str3 = null;
      } 
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("notificationChannelImportance");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof Integer) {
        Integer integer = (Integer)param1Context;
      } else {
        param1Context = null;
      } 
      if (param1Context != null) {
        b = param1Context.intValue();
      } else {
        b = 3;
      } 
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("notificationPriority");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof Integer) {
        Integer integer = (Integer)param1Context;
      } else {
        param1Context = null;
      } 
      if (param1Context != null) {
        bool1 = param1Context.intValue();
      } else {
        bool1 = false;
      } 
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("notificationContentTitle");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof String) {
        String str = (String)param1Context;
      } else {
        param1Context = null;
      } 
      if (param1Context == null) {
        String str = "";
      } else {
        context1 = param1Context;
      } 
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("notificationContentText");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof String) {
        String str = (String)param1Context;
      } else {
        param1Context = null;
      } 
      Context context2 = param1Context;
      if (param1Context == null)
        str4 = ""; 
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("enableVibration");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof Boolean) {
        Boolean bool = (Boolean)param1Context;
      } else {
        param1Context = null;
      } 
      if (param1Context != null) {
        bool3 = param1Context.booleanValue();
      } else {
        bool3 = false;
      } 
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("playSound");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof Boolean) {
        Boolean bool = (Boolean)param1Context;
      } else {
        param1Context = null;
      } 
      if (param1Context != null) {
        bool4 = param1Context.booleanValue();
      } else {
        bool4 = false;
      } 
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("showWhen");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof Boolean) {
        Boolean bool = (Boolean)param1Context;
      } else {
        param1Context = null;
      } 
      if (param1Context != null) {
        bool5 = param1Context.booleanValue();
      } else {
        bool5 = false;
      } 
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("isSticky");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof Boolean) {
        Boolean bool = (Boolean)param1Context;
      } else {
        param1Context = null;
      } 
      if (param1Context != null) {
        bool6 = param1Context.booleanValue();
      } else {
        bool6 = true;
      } 
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("visibility");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof Integer) {
        Integer integer = (Integer)param1Context;
      } else {
        param1Context = null;
      } 
      if (param1Context != null) {
        bool2 = param1Context.intValue();
      } else {
        bool2 = true;
      } 
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("iconData");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof Map) {
        Map map = (Map)param1Context;
      } else {
        param1Context = null;
      } 
      if (param1Context != null) {
        String str = (new JSONObject((Map)param1Context)).toString();
      } else {
        param1Context = null;
      } 
      if (param1Map != null) {
        param1Map = (Map<?, ?>)param1Map.get("buttons");
      } else {
        param1Map = null;
      } 
      if (param1Map instanceof List) {
        List list = (List)param1Map;
      } else {
        param1Map = null;
      } 
      if (param1Map != null) {
        String str = (new JSONArray((Collection)param1Map)).toString();
      } else {
        param1Map = null;
      } 
      SharedPreferences.Editor editor = sharedPreferences.edit();
      editor.putInt("notificationId", c);
      editor.putString("notificationChannelId", str1);
      editor.putString("notificationChannelName", str2);
      editor.putString("notificationChannelDescription", str3);
      editor.putInt("notificationChannelImportance", b);
      editor.putInt("notificationPriority", bool1);
      editor.putString("notificationContentTitle", (String)context1);
      editor.putString("notificationContentText", str4);
      editor.putBoolean("enableVibration", bool3);
      editor.putBoolean("playSound", bool4);
      editor.putBoolean("showWhen", bool5);
      editor.putBoolean("isSticky", bool6);
      editor.putInt("visibility", bool2);
      editor.putString("iconData", (String)param1Context);
      editor.putString("buttons", (String)param1Map);
      editor.commit();
    }
    
    public final void d(Context param1Context, Map<?, ?> param1Map) {
      String str1;
      String str2;
      Context context2;
      m.e(param1Context, "context");
      SharedPreferences sharedPreferences = param1Context.getSharedPreferences("com.pravera.flutter_foreground_task.prefs.NOTIFICATION_OPTIONS", 0);
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("notificationContentTitle");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof String) {
        context2 = param1Context;
      } else {
        context2 = null;
      } 
      String str3 = "";
      param1Context = context2;
      if (context2 == null) {
        String str = sharedPreferences.getString("notificationContentTitle", null);
        str1 = str;
        if (str == null)
          str1 = ""; 
      } 
      if (param1Map != null) {
        param1Map = (Map<?, ?>)param1Map.get("notificationContentText");
      } else {
        param1Map = null;
      } 
      if (param1Map instanceof String) {
        String str = (String)param1Map;
      } else {
        context2 = null;
      } 
      Context context1 = context2;
      if (context2 == null) {
        String str = sharedPreferences.getString("notificationContentText", null);
        str2 = str;
        if (str == null)
          str2 = str3; 
      } 
      SharedPreferences.Editor editor = sharedPreferences.edit();
      editor.putString("notificationContentTitle", str1);
      editor.putString("notificationContentText", str2);
      editor.commit();
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ae\e.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */