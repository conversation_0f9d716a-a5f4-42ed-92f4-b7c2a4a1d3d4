package aa;

import android.util.SparseArray;
import b9.c;
import b9.g;
import b9.h;
import b9.i;
import b9.j;
import b9.v;
import b9.w;
import b9.y;
import i9.g;
import java.util.List;
import ta.k;
import u8.q1;
import v8.p1;
import va.d0;
import va.p0;
import va.w;

public final class e implements j, g {
  public static final g.a j = new d();
  
  public static final v k = new v();
  
  public final h a;
  
  public final int b;
  
  public final q1 c;
  
  public final SparseArray<a> d;
  
  public boolean e;
  
  public g.b f;
  
  public long g;
  
  public w h;
  
  public q1[] i;
  
  public e(h paramh, int paramInt, q1 paramq1) {
    this.a = paramh;
    this.b = paramInt;
    this.c = paramq1;
    this.d = new SparseArray();
  }
  
  public boolean a(i parami) {
    int k = this.a.i(parami, k);
    boolean bool2 = false;
    if (k != 1) {
      bool1 = true;
    } else {
      bool1 = false;
    } 
    va.a.f(bool1);
    boolean bool1 = bool2;
    if (k == 0)
      bool1 = true; 
    return bool1;
  }
  
  public y b(int paramInt1, int paramInt2) {
    a a2 = (a)this.d.get(paramInt1);
    a a1 = a2;
    if (a2 == null) {
      boolean bool;
      if (this.i == null) {
        bool = true;
      } else {
        bool = false;
      } 
      va.a.f(bool);
      if (paramInt2 == this.b) {
        q1 q11 = this.c;
      } else {
        a1 = null;
      } 
      a1 = new a(paramInt1, paramInt2, (q1)a1);
      a1.g(this.f, this.g);
      this.d.put(paramInt1, a1);
    } 
    return a1;
  }
  
  public q1[] c() {
    return this.i;
  }
  
  public c d() {
    w w1 = this.h;
    if (w1 instanceof c) {
      c c = (c)w1;
    } else {
      w1 = null;
    } 
    return (c)w1;
  }
  
  public void e(g.b paramb, long paramLong1, long paramLong2) {
    this.f = paramb;
    this.g = paramLong2;
    if (!this.e) {
      this.a.e(this);
      if (paramLong1 != -9223372036854775807L)
        this.a.a(0L, paramLong1); 
      this.e = true;
    } else {
      h h1 = this.a;
      long l = paramLong1;
      if (paramLong1 == -9223372036854775807L)
        l = 0L; 
      h1.a(0L, l);
      for (byte b1 = 0; b1 < this.d.size(); b1++)
        ((a)this.d.valueAt(b1)).g(paramb, paramLong2); 
    } 
  }
  
  public void j() {
    q1[] arrayOfQ1 = new q1[this.d.size()];
    for (byte b1 = 0; b1 < this.d.size(); b1++)
      arrayOfQ1[b1] = (q1)va.a.h(((a)this.d.valueAt(b1)).e); 
    this.i = arrayOfQ1;
  }
  
  public void p(w paramw) {
    this.h = paramw;
  }
  
  public void release() {
    this.a.release();
  }
  
  public static final class a implements y {
    public final int a;
    
    public final int b;
    
    public final q1 c;
    
    public final g d;
    
    public q1 e;
    
    public y f;
    
    public long g;
    
    public a(int param1Int1, int param1Int2, q1 param1q1) {
      this.a = param1Int1;
      this.b = param1Int2;
      this.c = param1q1;
      this.d = new g();
    }
    
    public void a(q1 param1q1) {
      q1 q12 = this.c;
      q1 q11 = param1q1;
      if (q12 != null)
        q11 = param1q1.j(q12); 
      this.e = q11;
      ((y)p0.j(this.f)).a(this.e);
    }
    
    public void d(long param1Long, int param1Int1, int param1Int2, int param1Int3, y.a param1a) {
      long l = this.g;
      if (l != -9223372036854775807L && param1Long >= l)
        this.f = (y)this.d; 
      ((y)p0.j(this.f)).d(param1Long, param1Int1, param1Int2, param1Int3, param1a);
    }
    
    public void e(d0 param1d0, int param1Int1, int param1Int2) {
      ((y)p0.j(this.f)).b(param1d0, param1Int1);
    }
    
    public int f(k param1k, int param1Int1, boolean param1Boolean, int param1Int2) {
      return ((y)p0.j(this.f)).c(param1k, param1Int1, param1Boolean);
    }
    
    public void g(g.b param1b, long param1Long) {
      if (param1b == null) {
        this.f = (y)this.d;
        return;
      } 
      this.g = param1Long;
      y y1 = param1b.b(this.a, this.b);
      this.f = y1;
      q1 q11 = this.e;
      if (q11 != null)
        y1.a(q11); 
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\aa\e.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */