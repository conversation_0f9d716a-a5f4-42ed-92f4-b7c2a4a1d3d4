package aa;

import b9.g;
import b9.y;
import va.s;
import y9.l0;

public final class c implements g.b {
  public final int[] a;
  
  public final l0[] b;
  
  public c(int[] paramArrayOfint, l0[] paramArrayOfl0) {
    this.a = paramArrayOfint;
    this.b = paramArrayOfl0;
  }
  
  public int[] a() {
    int[] arrayOfInt = new int[this.b.length];
    byte b1 = 0;
    while (true) {
      l0[] arrayOfL0 = this.b;
      if (b1 < arrayOfL0.length) {
        arrayOfInt[b1] = arrayOfL0[b1].G();
        b1++;
        continue;
      } 
      return arrayOfInt;
    } 
  }
  
  public y b(int paramInt1, int paramInt2) {
    paramInt1 = 0;
    while (true) {
      int[] arrayOfInt = this.a;
      if (paramInt1 < arrayOfInt.length) {
        if (paramInt2 == arrayOfInt[paramInt1])
          return (y)this.b[paramInt1]; 
        paramInt1++;
        continue;
      } 
      StringBuilder stringBuilder = new StringBuilder(36);
      stringBuilder.append("Unmatched track of type: ");
      stringBuilder.append(paramInt2);
      s.c("BaseMediaChunkOutput", stringBuilder.toString());
      return (y)new g();
    } 
  }
  
  public void c(long paramLong) {
    l0[] arrayOfL0 = this.b;
    int i = arrayOfL0.length;
    for (byte b1 = 0; b1 < i; b1++)
      arrayOfL0[b1].a0(paramLong); 
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\aa\c.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */