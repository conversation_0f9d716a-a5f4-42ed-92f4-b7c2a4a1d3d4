package aa;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import ta.f0;
import ta.g0;
import u8.e3;
import u8.q1;
import u8.r1;
import va.p0;
import va.s;
import y8.g;
import y9.b0;
import y9.l0;
import y9.m0;
import y9.n0;
import y9.o;
import y9.r;
import z8.u;
import z8.v;

public class i<T extends j> implements m0, n0, g0.b<f>, g0.f {
  public final int a;
  
  public final int[] b;
  
  public final q1[] c;
  
  public final boolean[] d;
  
  public final T e;
  
  public final n0.a<i<T>> f;
  
  public final b0.a g;
  
  public final f0 h;
  
  public final g0 i;
  
  public final h j;
  
  public final ArrayList<a> k;
  
  public final List<a> l;
  
  public final l0 m;
  
  public final l0[] n;
  
  public final c o;
  
  public f p;
  
  public q1 q;
  
  public b<T> r;
  
  public long s;
  
  public long t;
  
  public int u;
  
  public a v;
  
  public boolean w;
  
  public i(int paramInt, int[] paramArrayOfint, q1[] paramArrayOfq1, T paramT, n0.a<i<T>> parama, ta.b paramb, long paramLong, v paramv, u.a parama1, f0 paramf0, b0.a parama2) {
    this.a = paramInt;
    int k = 0;
    int[] arrayOfInt2 = paramArrayOfint;
    if (paramArrayOfint == null)
      arrayOfInt2 = new int[0]; 
    this.b = arrayOfInt2;
    q1[] arrayOfQ1 = paramArrayOfq1;
    if (paramArrayOfq1 == null)
      arrayOfQ1 = new q1[0]; 
    this.c = arrayOfQ1;
    this.e = paramT;
    this.f = parama;
    this.g = parama2;
    this.h = paramf0;
    this.i = new g0("ChunkSampleStream");
    this.j = new h();
    ArrayList<a> arrayList = new ArrayList();
    this.k = arrayList;
    this.l = Collections.unmodifiableList(arrayList);
    int m = arrayOfInt2.length;
    this.n = new l0[m];
    this.d = new boolean[m];
    int n = m + 1;
    int[] arrayOfInt1 = new int[n];
    l0[] arrayOfL0 = new l0[n];
    l0 l01 = l0.k(paramb, paramv, parama1);
    this.m = l01;
    arrayOfInt1[0] = paramInt;
    arrayOfL0[0] = l01;
    for (paramInt = k; paramInt < m; paramInt = k) {
      l01 = l0.l(paramb);
      this.n[paramInt] = l01;
      k = paramInt + 1;
      arrayOfL0[k] = l01;
      arrayOfInt1[k] = this.b[paramInt];
    } 
    this.o = new c(arrayOfInt1, arrayOfL0);
    this.s = paramLong;
    this.t = paramLong;
  }
  
  public final void B(int paramInt) {
    paramInt = Math.min(O(paramInt, 0), this.u);
    if (paramInt > 0) {
      p0.L0(this.k, 0, paramInt);
      this.u -= paramInt;
    } 
  }
  
  public final void C(int paramInt) {
    // Byte code:
    //   0: aload_0
    //   1: getfield i : Lta/g0;
    //   4: invokevirtual j : ()Z
    //   7: iconst_1
    //   8: ixor
    //   9: invokestatic f : (Z)V
    //   12: aload_0
    //   13: getfield k : Ljava/util/ArrayList;
    //   16: invokevirtual size : ()I
    //   19: istore_2
    //   20: iload_1
    //   21: iload_2
    //   22: if_icmpge -> 42
    //   25: aload_0
    //   26: iload_1
    //   27: invokevirtual G : (I)Z
    //   30: ifne -> 36
    //   33: goto -> 44
    //   36: iinc #1, 1
    //   39: goto -> 20
    //   42: iconst_m1
    //   43: istore_1
    //   44: iload_1
    //   45: iconst_m1
    //   46: if_icmpne -> 50
    //   49: return
    //   50: aload_0
    //   51: invokevirtual F : ()Laa/a;
    //   54: getfield h : J
    //   57: lstore_3
    //   58: aload_0
    //   59: iload_1
    //   60: invokevirtual D : (I)Laa/a;
    //   63: astore #5
    //   65: aload_0
    //   66: getfield k : Ljava/util/ArrayList;
    //   69: invokevirtual isEmpty : ()Z
    //   72: ifeq -> 83
    //   75: aload_0
    //   76: aload_0
    //   77: getfield t : J
    //   80: putfield s : J
    //   83: aload_0
    //   84: iconst_0
    //   85: putfield w : Z
    //   88: aload_0
    //   89: getfield g : Ly9/b0$a;
    //   92: aload_0
    //   93: getfield a : I
    //   96: aload #5
    //   98: getfield g : J
    //   101: lload_3
    //   102: invokevirtual D : (IJJ)V
    //   105: return
  }
  
  public final a D(int paramInt) {
    a a1 = this.k.get(paramInt);
    ArrayList<a> arrayList = this.k;
    p0.L0(arrayList, paramInt, arrayList.size());
    this.u = Math.max(this.u, this.k.size());
    l0 l01 = this.m;
    paramInt = 0;
    while (true) {
      l01.u(a1.i(paramInt));
      l0[] arrayOfL0 = this.n;
      if (paramInt < arrayOfL0.length) {
        l0 l02 = arrayOfL0[paramInt];
        paramInt++;
        continue;
      } 
      return a1;
    } 
  }
  
  public T E() {
    return this.e;
  }
  
  public final a F() {
    ArrayList<a> arrayList = this.k;
    return arrayList.get(arrayList.size() - 1);
  }
  
  public final boolean G(int paramInt) {
    a a1 = this.k.get(paramInt);
    if (this.m.C() > a1.i(0))
      return true; 
    paramInt = 0;
    while (true) {
      l0[] arrayOfL0 = this.n;
      if (paramInt < arrayOfL0.length) {
        int m = arrayOfL0[paramInt].C();
        int k = paramInt + 1;
        paramInt = k;
        if (m > a1.i(k))
          return true; 
        continue;
      } 
      return false;
    } 
  }
  
  public final boolean H(f paramf) {
    return paramf instanceof a;
  }
  
  public boolean I() {
    boolean bool;
    if (this.s != -9223372036854775807L) {
      bool = true;
    } else {
      bool = false;
    } 
    return bool;
  }
  
  public final void J() {
    int k = O(this.m.C(), this.u - 1);
    while (true) {
      int m = this.u;
      if (m <= k) {
        this.u = m + 1;
        K(m);
        continue;
      } 
      break;
    } 
  }
  
  public final void K(int paramInt) {
    a a1 = this.k.get(paramInt);
    q1 q11 = a1.d;
    if (!q11.equals(this.q))
      this.g.i(this.a, q11, a1.e, a1.f, a1.g); 
    this.q = q11;
  }
  
  public void L(f paramf, long paramLong1, long paramLong2, boolean paramBoolean) {
    this.p = null;
    this.v = null;
    o o = new o(paramf.a, paramf.b, paramf.f(), paramf.e(), paramLong1, paramLong2, paramf.c());
    this.h.a(paramf.a);
    this.g.r(o, paramf.c, this.a, paramf.d, paramf.e, paramf.f, paramf.g, paramf.h);
    if (!paramBoolean) {
      if (I()) {
        R();
      } else if (H(paramf)) {
        D(this.k.size() - 1);
        if (this.k.isEmpty())
          this.s = this.t; 
      } 
      this.f.q(this);
    } 
  }
  
  public void M(f paramf, long paramLong1, long paramLong2) {
    this.p = null;
    this.e.n(paramf);
    o o = new o(paramf.a, paramf.b, paramf.f(), paramf.e(), paramLong1, paramLong2, paramf.c());
    this.h.a(paramf.a);
    this.g.u(o, paramf.c, this.a, paramf.d, paramf.e, paramf.f, paramf.g, paramf.h);
    this.f.q(this);
  }
  
  public g0.c N(f paramf, long paramLong1, long paramLong2, IOException paramIOException, int paramInt) {
    int m;
    long l = paramf.c();
    boolean bool = H(paramf);
    int k = this.k.size() - 1;
    if (l == 0L || !bool || !G(k)) {
      m = 1;
    } else {
      m = 0;
    } 
    o o = new o(paramf.a, paramf.b, paramf.f(), paramf.e(), paramLong1, paramLong2, l);
    f0.c c1 = new f0.c(o, new r(paramf.c, this.a, paramf.d, paramf.e, paramf.f, p0.b1(paramf.g), p0.b1(paramf.h)), paramIOException, paramInt);
    if (this.e.q(paramf, m, c1, this.h)) {
      g0.c c2;
      if (m) {
        g0.c c4 = g0.f;
        c2 = c4;
        if (bool) {
          if (D(k) == paramf) {
            m = 1;
          } else {
            m = 0;
          } 
          va.a.f(m);
          c2 = c4;
          if (this.k.isEmpty()) {
            this.s = this.t;
            c2 = c4;
          } 
        } 
      } else {
        s.i("ChunkSampleStream", "Ignoring attempt to cancel non-cancelable load.");
        c2 = null;
      } 
      g0.c c3 = c2;
      if (c2 == null) {
        paramLong1 = this.h.c(c1);
        if (paramLong1 != -9223372036854775807L) {
          c3 = g0.h(false, paramLong1);
        } else {
          c3 = g0.g;
        } 
      } 
      m = c3.c() ^ true;
      this.g.w(o, paramf.c, this.a, paramf.d, paramf.e, paramf.f, paramf.g, paramf.h, paramIOException, m);
      if (m != 0) {
        this.p = null;
        this.h.a(paramf.a);
        this.f.q(this);
      } 
      return c3;
    } 
    Object object = null;
  }
  
  public final int O(int paramInt1, int paramInt2) {
    while (true) {
      int k = paramInt2 + 1;
      if (k < this.k.size()) {
        paramInt2 = k;
        if (((a)this.k.get(k)).i(0) > paramInt1)
          return k - 1; 
        continue;
      } 
      return this.k.size() - 1;
    } 
  }
  
  public void P() {
    Q(null);
  }
  
  public void Q(b<T> paramb) {
    this.r = paramb;
    this.m.R();
    l0[] arrayOfL0 = this.n;
    int k = arrayOfL0.length;
    for (byte b1 = 0; b1 < k; b1++)
      arrayOfL0[b1].R(); 
    this.i.m(this);
  }
  
  public final void R() {
    this.m.V();
    l0[] arrayOfL0 = this.n;
    int k = arrayOfL0.length;
    for (byte b1 = 0; b1 < k; b1++)
      arrayOfL0[b1].V(); 
  }
  
  public void S(long paramLong) {
    // Byte code:
    //   0: aload_0
    //   1: lload_1
    //   2: putfield t : J
    //   5: aload_0
    //   6: invokevirtual I : ()Z
    //   9: ifeq -> 18
    //   12: aload_0
    //   13: lload_1
    //   14: putfield s : J
    //   17: return
    //   18: iconst_0
    //   19: istore #4
    //   21: iconst_0
    //   22: istore #5
    //   24: iconst_0
    //   25: istore_3
    //   26: iload_3
    //   27: aload_0
    //   28: getfield k : Ljava/util/ArrayList;
    //   31: invokevirtual size : ()I
    //   34: if_icmpge -> 93
    //   37: aload_0
    //   38: getfield k : Ljava/util/ArrayList;
    //   41: iload_3
    //   42: invokevirtual get : (I)Ljava/lang/Object;
    //   45: checkcast aa/a
    //   48: astore #8
    //   50: aload #8
    //   52: getfield g : J
    //   55: lload_1
    //   56: lcmp
    //   57: istore #6
    //   59: iload #6
    //   61: ifne -> 79
    //   64: aload #8
    //   66: getfield k : J
    //   69: ldc2_w -9223372036854775807
    //   72: lcmp
    //   73: ifne -> 79
    //   76: goto -> 96
    //   79: iload #6
    //   81: ifle -> 87
    //   84: goto -> 93
    //   87: iinc #3, 1
    //   90: goto -> 26
    //   93: aconst_null
    //   94: astore #8
    //   96: aload #8
    //   98: ifnull -> 119
    //   101: aload_0
    //   102: getfield m : Ly9/l0;
    //   105: aload #8
    //   107: iconst_0
    //   108: invokevirtual i : (I)I
    //   111: invokevirtual Y : (I)Z
    //   114: istore #7
    //   116: goto -> 153
    //   119: aload_0
    //   120: getfield m : Ly9/l0;
    //   123: astore #8
    //   125: lload_1
    //   126: aload_0
    //   127: invokevirtual c : ()J
    //   130: lcmp
    //   131: ifge -> 140
    //   134: iconst_1
    //   135: istore #7
    //   137: goto -> 143
    //   140: iconst_0
    //   141: istore #7
    //   143: aload #8
    //   145: lload_1
    //   146: iload #7
    //   148: invokevirtual Z : (JZ)Z
    //   151: istore #7
    //   153: iload #7
    //   155: ifeq -> 210
    //   158: aload_0
    //   159: aload_0
    //   160: aload_0
    //   161: getfield m : Ly9/l0;
    //   164: invokevirtual C : ()I
    //   167: iconst_0
    //   168: invokevirtual O : (II)I
    //   171: putfield u : I
    //   174: aload_0
    //   175: getfield n : [Ly9/l0;
    //   178: astore #8
    //   180: aload #8
    //   182: arraylength
    //   183: istore #4
    //   185: iload #5
    //   187: istore_3
    //   188: iload_3
    //   189: iload #4
    //   191: if_icmpge -> 303
    //   194: aload #8
    //   196: iload_3
    //   197: aaload
    //   198: lload_1
    //   199: iconst_1
    //   200: invokevirtual Z : (JZ)Z
    //   203: pop
    //   204: iinc #3, 1
    //   207: goto -> 188
    //   210: aload_0
    //   211: lload_1
    //   212: putfield s : J
    //   215: aload_0
    //   216: iconst_0
    //   217: putfield w : Z
    //   220: aload_0
    //   221: getfield k : Ljava/util/ArrayList;
    //   224: invokevirtual clear : ()V
    //   227: aload_0
    //   228: iconst_0
    //   229: putfield u : I
    //   232: aload_0
    //   233: getfield i : Lta/g0;
    //   236: invokevirtual j : ()Z
    //   239: ifeq -> 292
    //   242: aload_0
    //   243: getfield m : Ly9/l0;
    //   246: invokevirtual r : ()V
    //   249: aload_0
    //   250: getfield n : [Ly9/l0;
    //   253: astore #8
    //   255: aload #8
    //   257: arraylength
    //   258: istore #5
    //   260: iload #4
    //   262: istore_3
    //   263: iload_3
    //   264: iload #5
    //   266: if_icmpge -> 282
    //   269: aload #8
    //   271: iload_3
    //   272: aaload
    //   273: invokevirtual r : ()V
    //   276: iinc #3, 1
    //   279: goto -> 263
    //   282: aload_0
    //   283: getfield i : Lta/g0;
    //   286: invokevirtual f : ()V
    //   289: goto -> 303
    //   292: aload_0
    //   293: getfield i : Lta/g0;
    //   296: invokevirtual g : ()V
    //   299: aload_0
    //   300: invokevirtual R : ()V
    //   303: return
  }
  
  public a T(long paramLong, int paramInt) {
    for (byte b1 = 0; b1 < this.n.length; b1++) {
      if (this.b[b1] == paramInt) {
        va.a.f(this.d[b1] ^ true);
        this.d[b1] = true;
        this.n[b1].Z(paramLong, true);
        return new a(this, this, this.n[b1], b1);
      } 
    } 
    throw new IllegalStateException();
  }
  
  public void a() {
    this.i.a();
    this.m.N();
    if (!this.i.j())
      this.e.a(); 
  }
  
  public boolean b() {
    boolean bool;
    if (!I() && this.m.K(this.w)) {
      bool = true;
    } else {
      bool = false;
    } 
    return bool;
  }
  
  public long c() {
    long l;
    if (I())
      return this.s; 
    if (this.w) {
      l = Long.MIN_VALUE;
    } else {
      l = (F()).h;
    } 
    return l;
  }
  
  public boolean d() {
    return this.i.j();
  }
  
  public long e() {
    if (this.w)
      return Long.MIN_VALUE; 
    if (I())
      return this.s; 
    long l2 = this.t;
    a a1 = F();
    if (!a1.h())
      if (this.k.size() > 1) {
        ArrayList<a> arrayList = this.k;
        a a2 = arrayList.get(arrayList.size() - 2);
      } else {
        a1 = null;
      }  
    long l1 = l2;
    if (a1 != null)
      l1 = Math.max(l2, a1.h); 
    return Math.max(l1, this.m.z());
  }
  
  public void f(long paramLong) {
    if (!this.i.i() && !I()) {
      if (this.i.j()) {
        f f1 = (f)va.a.e(this.p);
        if (H(f1) && G(this.k.size() - 1))
          return; 
        if (this.e.p(paramLong, f1, (List)this.l)) {
          this.i.f();
          if (H(f1))
            this.v = (a)f1; 
        } 
        return;
      } 
      int k = this.e.m(paramLong, (List)this.l);
      if (k < this.k.size())
        C(k); 
    } 
  }
  
  public void g() {
    this.m.T();
    l0[] arrayOfL0 = this.n;
    int k = arrayOfL0.length;
    for (byte b1 = 0; b1 < k; b1++)
      arrayOfL0[b1].T(); 
    this.e.release();
    b<T> b2 = this.r;
    if (b2 != null)
      b2.b(this); 
  }
  
  public int j(long paramLong) {
    if (I())
      return 0; 
    int m = this.m.E(paramLong, this.w);
    a a1 = this.v;
    int k = m;
    if (a1 != null)
      k = Math.min(m, a1.i(0) - this.m.C()); 
    this.m.e0(k);
    J();
    return k;
  }
  
  public boolean k(long paramLong) {
    long l;
    List<a> list;
    boolean bool1 = this.w;
    byte b1 = 0;
    if (bool1 || this.i.j() || this.i.i())
      return false; 
    bool1 = I();
    if (bool1) {
      list = Collections.emptyList();
      l = this.s;
    } else {
      list = this.l;
      l = (F()).h;
    } 
    this.e.o(paramLong, l, (List)list, this.j);
    h h1 = this.j;
    boolean bool2 = h1.b;
    f f1 = h1.a;
    h1.a();
    if (bool2) {
      this.s = -9223372036854775807L;
      this.w = true;
      return true;
    } 
    if (f1 == null)
      return false; 
    this.p = f1;
    if (H(f1)) {
      a a1 = (a)f1;
      if (bool1) {
        l = a1.g;
        paramLong = this.s;
        if (l != paramLong) {
          this.m.b0(paramLong);
          l0[] arrayOfL0 = this.n;
          int k = arrayOfL0.length;
          while (b1 < k) {
            arrayOfL0[b1].b0(this.s);
            b1++;
          } 
        } 
        this.s = -9223372036854775807L;
      } 
      a1.k(this.o);
      this.k.add(a1);
    } else if (f1 instanceof m) {
      ((m)f1).g(this.o);
    } 
    paramLong = this.i.n(f1, this, this.h.b(f1.c));
    this.g.A(new o(f1.a, f1.b, paramLong), f1.c, this.a, f1.d, f1.e, f1.f, f1.g, f1.h);
    return true;
  }
  
  public long l(long paramLong, e3 parame3) {
    return this.e.l(paramLong, parame3);
  }
  
  public void o(long paramLong, boolean paramBoolean) {
    if (I())
      return; 
    int k = this.m.x();
    this.m.q(paramLong, paramBoolean, true);
    int m = this.m.x();
    if (m > k) {
      paramLong = this.m.y();
      k = 0;
      while (true) {
        l0[] arrayOfL0 = this.n;
        if (k < arrayOfL0.length) {
          arrayOfL0[k].q(paramLong, paramBoolean, this.d[k]);
          k++;
          continue;
        } 
        break;
      } 
    } 
    B(m);
  }
  
  public int p(r1 paramr1, g paramg, int paramInt) {
    if (I())
      return -3; 
    a a1 = this.v;
    if (a1 != null && a1.i(0) <= this.m.C())
      return -3; 
    J();
    return this.m.S(paramr1, paramg, paramInt, this.w);
  }
  
  public final class a implements m0 {
    public final i<T> a;
    
    public final l0 b;
    
    public final int c;
    
    public boolean d;
    
    public final i e;
    
    public a(i this$0, i<T> param1i, l0 param1l0, int param1Int) {
      this.a = param1i;
      this.b = param1l0;
      this.c = param1Int;
    }
    
    public void a() {}
    
    public boolean b() {
      boolean bool;
      if (!this.e.I() && this.b.K(this.e.w)) {
        bool = true;
      } else {
        bool = false;
      } 
      return bool;
    }
    
    public final void c() {
      if (!this.d) {
        i.A(this.e).i(i.x(this.e)[this.c], i.y(this.e)[this.c], 0, null, i.z(this.e));
        this.d = true;
      } 
    }
    
    public void d() {
      va.a.f(i.w(this.e)[this.c]);
      i.w(this.e)[this.c] = false;
    }
    
    public int j(long param1Long) {
      if (this.e.I())
        return 0; 
      int k = this.b.E(param1Long, this.e.w);
      int j = k;
      if (i.v(this.e) != null)
        j = Math.min(k, i.v(this.e).i(this.c + 1) - this.b.C()); 
      this.b.e0(j);
      if (j > 0)
        c(); 
      return j;
    }
    
    public int p(r1 param1r1, g param1g, int param1Int) {
      if (this.e.I())
        return -3; 
      if (i.v(this.e) != null && i.v(this.e).i(this.c + 1) <= this.b.C())
        return -3; 
      c();
      return this.b.S(param1r1, param1g, param1Int, this.e.w);
    }
  }
  
  public static interface b<T extends j> {
    void b(i<T> param1i);
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\aa\i.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */