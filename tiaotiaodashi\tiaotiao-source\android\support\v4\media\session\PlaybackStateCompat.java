package android.support.v4.media.session;

import android.media.session.PlaybackState;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.os.SystemClock;
import android.text.TextUtils;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public final class PlaybackStateCompat implements Parcelable {
  public static final Parcelable.Creator<PlaybackStateCompat> CREATOR = new a();
  
  public final int a;
  
  public final long b;
  
  public final long c;
  
  public final float d;
  
  public final long e;
  
  public final int f;
  
  public final CharSequence g;
  
  public final long h;
  
  public List<CustomAction> i;
  
  public final long j;
  
  public final Bundle k;
  
  public PlaybackState l;
  
  public PlaybackStateCompat(int paramInt1, long paramLong1, long paramLong2, float paramFloat, long paramLong3, int paramInt2, CharSequence paramCharSequence, long paramLong4, List<CustomAction> paramList, long paramLong5, Bundle paramBundle) {
    this.a = paramInt1;
    this.b = paramLong1;
    this.c = paramLong2;
    this.d = paramFloat;
    this.e = paramLong3;
    this.f = paramInt2;
    this.g = paramCharSequence;
    this.h = paramLong4;
    this.i = new ArrayList<CustomAction>(paramList);
    this.j = paramLong5;
    this.k = paramBundle;
  }
  
  public PlaybackStateCompat(Parcel paramParcel) {
    this.a = paramParcel.readInt();
    this.b = paramParcel.readLong();
    this.d = paramParcel.readFloat();
    this.h = paramParcel.readLong();
    this.c = paramParcel.readLong();
    this.e = paramParcel.readLong();
    this.g = (CharSequence)TextUtils.CHAR_SEQUENCE_CREATOR.createFromParcel(paramParcel);
    this.i = paramParcel.createTypedArrayList(CustomAction.CREATOR);
    this.j = paramParcel.readLong();
    this.k = paramParcel.readBundle(MediaSessionCompat.class.getClassLoader());
    this.f = paramParcel.readInt();
  }
  
  public static PlaybackStateCompat c(Object<PlaybackState.CustomAction> paramObject) {
    PlaybackStateCompat playbackStateCompat;
    ArrayList<CustomAction> arrayList = null;
    Iterator<PlaybackState.CustomAction> iterator = null;
    if (paramObject != null) {
      PlaybackState playbackState = (PlaybackState)paramObject;
      List<PlaybackState.CustomAction> list = b.j(playbackState);
      paramObject = (Object<PlaybackState.CustomAction>)iterator;
      if (list != null) {
        arrayList = new ArrayList(list.size());
        iterator = list.iterator();
        while (true) {
          paramObject = (Object)arrayList;
          if (iterator.hasNext()) {
            arrayList.add(CustomAction.c(iterator.next()));
            continue;
          } 
          break;
        } 
      } 
      Bundle bundle = c.a(playbackState);
      MediaSessionCompat.a(bundle);
      playbackStateCompat = new PlaybackStateCompat(b.r(playbackState), b.q(playbackState), b.i(playbackState), b.p(playbackState), b.g(playbackState), 0, b.k(playbackState), b.n(playbackState), (List)paramObject, b.h(playbackState), bundle);
      playbackStateCompat.l = playbackState;
    } 
    return playbackStateCompat;
  }
  
  public long d() {
    return this.e;
  }
  
  public int describeContents() {
    return 0;
  }
  
  public long g() {
    return this.j;
  }
  
  public long h() {
    return this.h;
  }
  
  public float i() {
    return this.d;
  }
  
  public Object j() {
    if (this.l == null) {
      PlaybackState.Builder builder = b.d();
      b.x(builder, this.a, this.b, this.d, this.h);
      b.u(builder, this.c);
      b.s(builder, this.e);
      b.v(builder, this.g);
      Iterator<CustomAction> iterator = this.i.iterator();
      while (iterator.hasNext())
        b.a(builder, (PlaybackState.CustomAction)((CustomAction)iterator.next()).g()); 
      b.t(builder, this.j);
      c.b(builder, this.k);
      this.l = b.c(builder);
    } 
    return this.l;
  }
  
  public long k() {
    return this.b;
  }
  
  public int l() {
    return this.a;
  }
  
  public String toString() {
    StringBuilder stringBuilder = new StringBuilder("PlaybackState {");
    stringBuilder.append("state=");
    stringBuilder.append(this.a);
    stringBuilder.append(", position=");
    stringBuilder.append(this.b);
    stringBuilder.append(", buffered position=");
    stringBuilder.append(this.c);
    stringBuilder.append(", speed=");
    stringBuilder.append(this.d);
    stringBuilder.append(", updated=");
    stringBuilder.append(this.h);
    stringBuilder.append(", actions=");
    stringBuilder.append(this.e);
    stringBuilder.append(", error code=");
    stringBuilder.append(this.f);
    stringBuilder.append(", error message=");
    stringBuilder.append(this.g);
    stringBuilder.append(", custom actions=");
    stringBuilder.append(this.i);
    stringBuilder.append(", active item id=");
    stringBuilder.append(this.j);
    stringBuilder.append("}");
    return stringBuilder.toString();
  }
  
  public void writeToParcel(Parcel paramParcel, int paramInt) {
    paramParcel.writeInt(this.a);
    paramParcel.writeLong(this.b);
    paramParcel.writeFloat(this.d);
    paramParcel.writeLong(this.h);
    paramParcel.writeLong(this.c);
    paramParcel.writeLong(this.e);
    TextUtils.writeToParcel(this.g, paramParcel, paramInt);
    paramParcel.writeTypedList(this.i);
    paramParcel.writeLong(this.j);
    paramParcel.writeBundle(this.k);
    paramParcel.writeInt(this.f);
  }
  
  public static final class CustomAction implements Parcelable {
    public static final Parcelable.Creator<CustomAction> CREATOR = new a();
    
    public final String a;
    
    public final CharSequence b;
    
    public final int c;
    
    public final Bundle d;
    
    public PlaybackState.CustomAction e;
    
    public CustomAction(Parcel param1Parcel) {
      this.a = param1Parcel.readString();
      this.b = (CharSequence)TextUtils.CHAR_SEQUENCE_CREATOR.createFromParcel(param1Parcel);
      this.c = param1Parcel.readInt();
      this.d = param1Parcel.readBundle(MediaSessionCompat.class.getClassLoader());
    }
    
    public CustomAction(String param1String, CharSequence param1CharSequence, int param1Int, Bundle param1Bundle) {
      this.a = param1String;
      this.b = param1CharSequence;
      this.c = param1Int;
      this.d = param1Bundle;
    }
    
    public static CustomAction c(Object param1Object) {
      if (param1Object != null) {
        param1Object = param1Object;
        Bundle bundle = PlaybackStateCompat.b.l((PlaybackState.CustomAction)param1Object);
        MediaSessionCompat.a(bundle);
        CustomAction customAction = new CustomAction(PlaybackStateCompat.b.f((PlaybackState.CustomAction)param1Object), PlaybackStateCompat.b.o((PlaybackState.CustomAction)param1Object), PlaybackStateCompat.b.m((PlaybackState.CustomAction)param1Object), bundle);
        customAction.e = (PlaybackState.CustomAction)param1Object;
        return customAction;
      } 
      return null;
    }
    
    public String d() {
      return this.a;
    }
    
    public int describeContents() {
      return 0;
    }
    
    public Object g() {
      PlaybackState.CustomAction customAction2 = this.e;
      PlaybackState.CustomAction customAction1 = customAction2;
      if (customAction2 == null) {
        PlaybackState.CustomAction.Builder builder = PlaybackStateCompat.b.e(this.a, this.b, this.c);
        PlaybackStateCompat.b.w(builder, this.d);
        customAction1 = PlaybackStateCompat.b.b(builder);
      } 
      return customAction1;
    }
    
    public String toString() {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("Action:mName='");
      stringBuilder.append(this.b);
      stringBuilder.append(", mIcon=");
      stringBuilder.append(this.c);
      stringBuilder.append(", mExtras=");
      stringBuilder.append(this.d);
      return stringBuilder.toString();
    }
    
    public void writeToParcel(Parcel param1Parcel, int param1Int) {
      param1Parcel.writeString(this.a);
      TextUtils.writeToParcel(this.b, param1Parcel, param1Int);
      param1Parcel.writeInt(this.c);
      param1Parcel.writeBundle(this.d);
    }
    
    public class a implements Parcelable.Creator<CustomAction> {
      public PlaybackStateCompat.CustomAction a(Parcel param2Parcel) {
        return new PlaybackStateCompat.CustomAction(param2Parcel);
      }
      
      public PlaybackStateCompat.CustomAction[] b(int param2Int) {
        return new PlaybackStateCompat.CustomAction[param2Int];
      }
    }
  }
  
  public class a implements Parcelable.Creator<CustomAction> {
    public PlaybackStateCompat.CustomAction a(Parcel param1Parcel) {
      return new PlaybackStateCompat.CustomAction(param1Parcel);
    }
    
    public PlaybackStateCompat.CustomAction[] b(int param1Int) {
      return new PlaybackStateCompat.CustomAction[param1Int];
    }
  }
  
  public class a implements Parcelable.Creator<PlaybackStateCompat> {
    public PlaybackStateCompat a(Parcel param1Parcel) {
      return new PlaybackStateCompat(param1Parcel);
    }
    
    public PlaybackStateCompat[] b(int param1Int) {
      return new PlaybackStateCompat[param1Int];
    }
  }
  
  public static class b {
    public static void a(PlaybackState.Builder param1Builder, PlaybackState.CustomAction param1CustomAction) {
      param1Builder.addCustomAction(param1CustomAction);
    }
    
    public static PlaybackState.CustomAction b(PlaybackState.CustomAction.Builder param1Builder) {
      return param1Builder.build();
    }
    
    public static PlaybackState c(PlaybackState.Builder param1Builder) {
      return param1Builder.build();
    }
    
    public static PlaybackState.Builder d() {
      return new PlaybackState.Builder();
    }
    
    public static PlaybackState.CustomAction.Builder e(String param1String, CharSequence param1CharSequence, int param1Int) {
      return new PlaybackState.CustomAction.Builder(param1String, param1CharSequence, param1Int);
    }
    
    public static String f(PlaybackState.CustomAction param1CustomAction) {
      return param1CustomAction.getAction();
    }
    
    public static long g(PlaybackState param1PlaybackState) {
      return param1PlaybackState.getActions();
    }
    
    public static long h(PlaybackState param1PlaybackState) {
      return param1PlaybackState.getActiveQueueItemId();
    }
    
    public static long i(PlaybackState param1PlaybackState) {
      return param1PlaybackState.getBufferedPosition();
    }
    
    public static List<PlaybackState.CustomAction> j(PlaybackState param1PlaybackState) {
      return param1PlaybackState.getCustomActions();
    }
    
    public static CharSequence k(PlaybackState param1PlaybackState) {
      return param1PlaybackState.getErrorMessage();
    }
    
    public static Bundle l(PlaybackState.CustomAction param1CustomAction) {
      return param1CustomAction.getExtras();
    }
    
    public static int m(PlaybackState.CustomAction param1CustomAction) {
      return param1CustomAction.getIcon();
    }
    
    public static long n(PlaybackState param1PlaybackState) {
      return param1PlaybackState.getLastPositionUpdateTime();
    }
    
    public static CharSequence o(PlaybackState.CustomAction param1CustomAction) {
      return param1CustomAction.getName();
    }
    
    public static float p(PlaybackState param1PlaybackState) {
      return param1PlaybackState.getPlaybackSpeed();
    }
    
    public static long q(PlaybackState param1PlaybackState) {
      return param1PlaybackState.getPosition();
    }
    
    public static int r(PlaybackState param1PlaybackState) {
      return param1PlaybackState.getState();
    }
    
    public static void s(PlaybackState.Builder param1Builder, long param1Long) {
      param1Builder.setActions(param1Long);
    }
    
    public static void t(PlaybackState.Builder param1Builder, long param1Long) {
      param1Builder.setActiveQueueItemId(param1Long);
    }
    
    public static void u(PlaybackState.Builder param1Builder, long param1Long) {
      param1Builder.setBufferedPosition(param1Long);
    }
    
    public static void v(PlaybackState.Builder param1Builder, CharSequence param1CharSequence) {
      param1Builder.setErrorMessage(param1CharSequence);
    }
    
    public static void w(PlaybackState.CustomAction.Builder param1Builder, Bundle param1Bundle) {
      param1Builder.setExtras(param1Bundle);
    }
    
    public static void x(PlaybackState.Builder param1Builder, int param1Int, long param1Long1, float param1Float, long param1Long2) {
      param1Builder.setState(param1Int, param1Long1, param1Float, param1Long2);
    }
  }
  
  public static class c {
    public static Bundle a(PlaybackState param1PlaybackState) {
      return param1PlaybackState.getExtras();
    }
    
    public static void b(PlaybackState.Builder param1Builder, Bundle param1Bundle) {
      param1Builder.setExtras(param1Bundle);
    }
  }
  
  public static final class d {
    public final List<PlaybackStateCompat.CustomAction> a;
    
    public int b;
    
    public long c;
    
    public long d;
    
    public float e;
    
    public long f;
    
    public int g;
    
    public CharSequence h;
    
    public long i;
    
    public long j;
    
    public Bundle k;
    
    public d() {
      this.a = new ArrayList<PlaybackStateCompat.CustomAction>();
      this.j = -1L;
    }
    
    public d(PlaybackStateCompat param1PlaybackStateCompat) {
      ArrayList<PlaybackStateCompat.CustomAction> arrayList = new ArrayList();
      this.a = arrayList;
      this.j = -1L;
      this.b = param1PlaybackStateCompat.a;
      this.c = param1PlaybackStateCompat.b;
      this.e = param1PlaybackStateCompat.d;
      this.i = param1PlaybackStateCompat.h;
      this.d = param1PlaybackStateCompat.c;
      this.f = param1PlaybackStateCompat.e;
      this.g = param1PlaybackStateCompat.f;
      this.h = param1PlaybackStateCompat.g;
      List<PlaybackStateCompat.CustomAction> list = param1PlaybackStateCompat.i;
      if (list != null)
        arrayList.addAll(list); 
      this.j = param1PlaybackStateCompat.j;
      this.k = param1PlaybackStateCompat.k;
    }
    
    public d a(PlaybackStateCompat.CustomAction param1CustomAction) {
      if (param1CustomAction != null) {
        this.a.add(param1CustomAction);
        return this;
      } 
      throw new IllegalArgumentException("You may not add a null CustomAction to PlaybackStateCompat");
    }
    
    public PlaybackStateCompat b() {
      return new PlaybackStateCompat(this.b, this.c, this.d, this.e, this.f, this.g, this.h, this.i, this.a, this.j, this.k);
    }
    
    public d c(long param1Long) {
      this.f = param1Long;
      return this;
    }
    
    public d d(long param1Long) {
      this.j = param1Long;
      return this;
    }
    
    public d e(long param1Long) {
      this.d = param1Long;
      return this;
    }
    
    public d f(int param1Int, CharSequence param1CharSequence) {
      this.g = param1Int;
      this.h = param1CharSequence;
      return this;
    }
    
    public d g(Bundle param1Bundle) {
      this.k = param1Bundle;
      return this;
    }
    
    public d h(int param1Int, long param1Long, float param1Float) {
      return i(param1Int, param1Long, param1Float, SystemClock.elapsedRealtime());
    }
    
    public d i(int param1Int, long param1Long1, float param1Float, long param1Long2) {
      this.b = param1Int;
      this.c = param1Long1;
      this.i = param1Long2;
      this.e = param1Float;
      return this;
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\android\support\v4\media\session\PlaybackStateCompat.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */