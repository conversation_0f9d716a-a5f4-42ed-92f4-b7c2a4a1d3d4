package a7;

import com.bumptech.glide.load.data.d;
import com.bumptech.glide.load.data.j;
import java.io.InputStream;
import t6.f;
import t6.g;
import t6.h;
import z6.h;
import z6.n;
import z6.o;
import z6.p;
import z6.s;

public class a implements o<h, InputStream> {
  public static final g<Integer> b = g.f("com.bumptech.glide.load.model.stream.HttpGlideUrlLoader.Timeout", Integer.valueOf(2500));
  
  public final n<h, h> a;
  
  public a(n<h, h> paramn) {
    this.a = paramn;
  }
  
  public o.a<InputStream> c(h paramh, int paramInt1, int paramInt2, h paramh1) {
    n<h, h> n1 = this.a;
    h h1 = paramh;
    if (n1 != null) {
      h1 = (h)n1.a(paramh, 0, 0);
      if (h1 == null) {
        this.a.b(paramh, 0, 0, paramh);
        h1 = paramh;
      } 
    } 
    return new o.a((f)h1, (d)new j(h1, ((Integer)paramh1.c(b)).intValue()));
  }
  
  public boolean d(h paramh) {
    return true;
  }
  
  public static class a implements p<h, InputStream> {
    public final n<h, h> a = new n(500L);
    
    public o<h, InputStream> c(s param1s) {
      return new a(this.a);
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a7\a.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */