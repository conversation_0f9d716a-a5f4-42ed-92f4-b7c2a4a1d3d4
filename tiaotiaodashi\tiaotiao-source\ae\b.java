package ae;

import android.content.Context;
import android.content.SharedPreferences;
import gh.m;
import java.util.Map;
import xg.g;
import xg.m;

public final class b {
  public static final a h = new a(null);
  
  public final long a;
  
  public final boolean b;
  
  public final boolean c;
  
  public final boolean d;
  
  public final boolean e;
  
  public final boolean f;
  
  public final Long g;
  
  public b(long paramLong, boolean paramBoolean1, boolean paramBoolean2, boolean paramBoolean3, boolean paramBoolean4, boolean paramBoolean5, Long paramLong1) {
    this.a = paramLong;
    this.b = paramBoolean1;
    this.c = paramBoolean2;
    this.d = paramBoolean3;
    this.e = paramBoolean4;
    this.f = paramBoolean5;
    this.g = paramLong1;
  }
  
  public final boolean a() {
    return this.e;
  }
  
  public final boolean b() {
    return this.f;
  }
  
  public final boolean c() {
    return this.c;
  }
  
  public final boolean d() {
    return this.d;
  }
  
  public final Long e() {
    return this.g;
  }
  
  public boolean equals(Object paramObject) {
    if (this == paramObject)
      return true; 
    if (!(paramObject instanceof b))
      return false; 
    paramObject = paramObject;
    return (this.a != ((b)paramObject).a) ? false : ((this.b != ((b)paramObject).b) ? false : ((this.c != ((b)paramObject).c) ? false : ((this.d != ((b)paramObject).d) ? false : ((this.e != ((b)paramObject).e) ? false : ((this.f != ((b)paramObject).f) ? false : (!!m.a(this.g, ((b)paramObject).g)))))));
  }
  
  public final long f() {
    return this.a;
  }
  
  public final boolean g() {
    return this.b;
  }
  
  public int hashCode() {
    int i;
    int j = Long.hashCode(this.a);
    int k = Boolean.hashCode(this.b);
    int i2 = Boolean.hashCode(this.c);
    int n = Boolean.hashCode(this.d);
    int i1 = Boolean.hashCode(this.e);
    int m = Boolean.hashCode(this.f);
    Long long_ = this.g;
    if (long_ == null) {
      i = 0;
    } else {
      i = long_.hashCode();
    } 
    return (((((j * 31 + k) * 31 + i2) * 31 + n) * 31 + i1) * 31 + m) * 31 + i;
  }
  
  public String toString() {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("ForegroundTaskOptions(interval=");
    stringBuilder.append(this.a);
    stringBuilder.append(", isOnceEvent=");
    stringBuilder.append(this.b);
    stringBuilder.append(", autoRunOnBoot=");
    stringBuilder.append(this.c);
    stringBuilder.append(", autoRunOnMyPackageReplaced=");
    stringBuilder.append(this.d);
    stringBuilder.append(", allowWakeLock=");
    stringBuilder.append(this.e);
    stringBuilder.append(", allowWifiLock=");
    stringBuilder.append(this.f);
    stringBuilder.append(", callbackHandle=");
    stringBuilder.append(this.g);
    stringBuilder.append(')');
    return stringBuilder.toString();
  }
  
  public static final class a {
    public a() {}
    
    public final void a(Context param1Context) {
      m.e(param1Context, "context");
      SharedPreferences.Editor editor = param1Context.getSharedPreferences("com.pravera.flutter_foreground_task.prefs.FOREGROUND_TASK_OPTIONS", 0).edit();
      editor.clear();
      editor.commit();
    }
    
    public final b b(Context param1Context) {
      m.e(param1Context, "context");
      SharedPreferences sharedPreferences = param1Context.getSharedPreferences("com.pravera.flutter_foreground_task.prefs.FOREGROUND_TASK_OPTIONS", 0);
      long l = sharedPreferences.getLong("interval", 5000L);
      boolean bool1 = sharedPreferences.getBoolean("isOnceEvent", false);
      boolean bool5 = sharedPreferences.getBoolean("autoRunOnBoot", false);
      boolean bool2 = sharedPreferences.getBoolean("autoRunOnMyPackageReplaced", false);
      boolean bool3 = sharedPreferences.getBoolean("allowWakeLock", true);
      boolean bool4 = sharedPreferences.getBoolean("allowWifiLock", false);
      if (sharedPreferences.contains("callbackHandle")) {
        Long long_ = Long.valueOf(sharedPreferences.getLong("callbackHandle", 0L));
      } else {
        sharedPreferences = null;
      } 
      return new b(l, bool1, bool5, bool2, bool3, bool4, (Long)sharedPreferences);
    }
    
    public final void c(Context param1Context, Map<?, ?> param1Map) {
      boolean bool1;
      boolean bool2;
      boolean bool3;
      boolean bool4;
      boolean bool5;
      long l;
      m.e(param1Context, "context");
      SharedPreferences sharedPreferences = param1Context.getSharedPreferences("com.pravera.flutter_foreground_task.prefs.FOREGROUND_TASK_OPTIONS", 0);
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("interval");
      } else {
        param1Context = null;
      } 
      Long long_ = m.k(String.valueOf(param1Context));
      if (long_ != null) {
        l = long_.longValue();
      } else {
        l = 5000L;
      } 
      if (param1Map != null) {
        long_ = (Long)param1Map.get("isOnceEvent");
      } else {
        long_ = null;
      } 
      if (long_ instanceof Boolean) {
        Boolean bool = (Boolean)long_;
      } else {
        long_ = null;
      } 
      if (long_ != null) {
        bool1 = long_.booleanValue();
      } else {
        bool1 = false;
      } 
      if (param1Map != null) {
        long_ = (Long)param1Map.get("autoRunOnBoot");
      } else {
        long_ = null;
      } 
      if (long_ instanceof Boolean) {
        Boolean bool = (Boolean)long_;
      } else {
        long_ = null;
      } 
      if (long_ != null) {
        bool2 = long_.booleanValue();
      } else {
        bool2 = false;
      } 
      if (param1Map != null) {
        long_ = (Long)param1Map.get("autoRunOnMyPackageReplaced");
      } else {
        long_ = null;
      } 
      if (long_ instanceof Boolean) {
        Boolean bool = (Boolean)long_;
      } else {
        long_ = null;
      } 
      if (long_ != null) {
        bool3 = long_.booleanValue();
      } else {
        bool3 = false;
      } 
      if (param1Map != null) {
        long_ = (Long)param1Map.get("allowWakeLock");
      } else {
        long_ = null;
      } 
      if (long_ instanceof Boolean) {
        Boolean bool = (Boolean)long_;
      } else {
        long_ = null;
      } 
      if (long_ != null) {
        bool4 = long_.booleanValue();
      } else {
        bool4 = true;
      } 
      if (param1Map != null) {
        long_ = (Long)param1Map.get("allowWifiLock");
      } else {
        long_ = null;
      } 
      if (long_ instanceof Boolean) {
        Boolean bool = (Boolean)long_;
      } else {
        long_ = null;
      } 
      if (long_ != null) {
        bool5 = long_.booleanValue();
      } else {
        bool5 = false;
      } 
      if (param1Map != null) {
        long_ = (Long)param1Map.get("callbackHandle");
      } else {
        long_ = null;
      } 
      long_ = m.k(String.valueOf(long_));
      SharedPreferences.Editor editor = sharedPreferences.edit();
      editor.putLong("interval", l);
      editor.putBoolean("isOnceEvent", bool1);
      editor.putBoolean("autoRunOnBoot", bool2);
      editor.putBoolean("autoRunOnMyPackageReplaced", bool3);
      editor.putBoolean("allowWakeLock", bool4);
      editor.putBoolean("allowWifiLock", bool5);
      editor.remove("callbackHandle");
      if (long_ != null)
        editor.putLong("callbackHandle", long_.longValue()); 
      editor.commit();
    }
    
    public final void d(Context param1Context, Map<?, ?> param1Map) {
      Boolean bool1;
      Boolean bool2;
      Boolean bool3;
      Boolean bool4;
      m.e(param1Context, "context");
      SharedPreferences sharedPreferences = param1Context.getSharedPreferences("com.pravera.flutter_foreground_task.prefs.FOREGROUND_TASK_OPTIONS", 0);
      Object object = null;
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("interval");
      } else {
        param1Context = null;
      } 
      Long long_2 = m.k(String.valueOf(param1Context));
      if (param1Map != null) {
        param1Context = (Context)param1Map.get("isOnceEvent");
      } else {
        param1Context = null;
      } 
      if (param1Context instanceof Boolean) {
        Boolean bool = (Boolean)param1Context;
      } else {
        param1Context = null;
      } 
      if (param1Map != null) {
        bool1 = (Boolean)param1Map.get("autoRunOnBoot");
      } else {
        bool1 = null;
      } 
      if (bool1 instanceof Boolean) {
        bool1 = bool1;
      } else {
        bool1 = null;
      } 
      if (param1Map != null) {
        bool2 = (Boolean)param1Map.get("autoRunOnMyPackageReplaced");
      } else {
        bool2 = null;
      } 
      if (bool2 instanceof Boolean) {
        bool2 = bool2;
      } else {
        bool2 = null;
      } 
      if (param1Map != null) {
        bool3 = (Boolean)param1Map.get("allowWakeLock");
      } else {
        bool3 = null;
      } 
      if (bool3 instanceof Boolean) {
        bool3 = bool3;
      } else {
        bool3 = null;
      } 
      if (param1Map != null) {
        bool4 = (Boolean)param1Map.get("allowWifiLock");
      } else {
        bool4 = null;
      } 
      if (bool4 instanceof Boolean) {
        bool4 = bool4;
      } else {
        bool4 = null;
      } 
      if (param1Map != null)
        object = param1Map.get("callbackHandle"); 
      Long long_1 = m.k(String.valueOf(object));
      object = sharedPreferences.edit();
      if (long_2 != null)
        object.putLong("interval", long_2.longValue()); 
      if (param1Context != null)
        object.putBoolean("isOnceEvent", param1Context.booleanValue()); 
      if (bool1 != null)
        object.putBoolean("autoRunOnBoot", bool1.booleanValue()); 
      if (bool2 != null)
        object.putBoolean("autoRunOnMyPackageReplaced", bool2.booleanValue()); 
      if (bool3 != null)
        object.putBoolean("allowWakeLock", bool3.booleanValue()); 
      if (bool4 != null)
        object.putBoolean("allowWifiLock", bool4.booleanValue()); 
      if (long_1 != null)
        object.putLong("callbackHandle", long_1.longValue()); 
      object.commit();
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ae\b.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */