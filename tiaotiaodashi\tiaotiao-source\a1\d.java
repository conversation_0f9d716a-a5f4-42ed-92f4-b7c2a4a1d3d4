package a1;

import android.graphics.Paint;
import android.graphics.Rect;

public final class d {
  public static final ThreadLocal<i1.d<Rect, Rect>> a = new ThreadLocal<i1.d<Rect, Rect>>();
  
  public static boolean a(Paint paramPaint, String paramString) {
    return a.a(paramPaint, paramString);
  }
  
  public static class a {
    public static boolean a(Paint param1Paint, String param1String) {
      return param1Paint.hasGlyph(param1String);
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a1\d.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */