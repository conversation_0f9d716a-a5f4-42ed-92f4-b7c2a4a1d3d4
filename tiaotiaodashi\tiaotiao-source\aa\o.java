package aa;

import java.util.NoSuchElementException;

public interface o {
  public static final o a = new a();
  
  long a();
  
  long b();
  
  boolean next();
  
  public class a implements o {
    public long a() {
      throw new NoSuchElementException();
    }
    
    public long b() {
      throw new NoSuchElementException();
    }
    
    public boolean next() {
      return false;
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\aa\o.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */