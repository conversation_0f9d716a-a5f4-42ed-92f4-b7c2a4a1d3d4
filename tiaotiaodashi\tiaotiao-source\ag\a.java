package ag;

import android.graphics.Rect;
import android.hardware.camera2.CaptureRequest;
import io.flutter.plugins.camera.y;
import io.flutter.plugins.camera.y1;
import pf.a;

public class a extends a<Float> {
  public static final Float g = Float.valueOf(1.0F);
  
  public final boolean b;
  
  public final Rect c;
  
  public Float d;
  
  public Float e;
  
  public final Float f;
  
  public a(y paramy) {
    // Byte code:
    //   0: aload_0
    //   1: aload_1
    //   2: invokespecial <init> : (Lio/flutter/plugins/camera/y;)V
    //   5: getstatic ag/a.g : Ljava/lang/Float;
    //   8: astore #4
    //   10: aload_0
    //   11: aload #4
    //   13: putfield d : Ljava/lang/Float;
    //   16: aload_0
    //   17: aload #4
    //   19: putfield e : Ljava/lang/Float;
    //   22: aload_1
    //   23: invokeinterface l : ()Landroid/graphics/Rect;
    //   28: astore_3
    //   29: aload_0
    //   30: aload_3
    //   31: putfield c : Landroid/graphics/Rect;
    //   34: iconst_0
    //   35: istore_2
    //   36: aload_3
    //   37: ifnonnull -> 54
    //   40: aload_0
    //   41: aload_0
    //   42: getfield e : Ljava/lang/Float;
    //   45: putfield f : Ljava/lang/Float;
    //   48: aload_0
    //   49: iconst_0
    //   50: putfield b : Z
    //   53: return
    //   54: invokestatic g : ()Z
    //   57: ifeq -> 85
    //   60: aload_0
    //   61: aload_1
    //   62: invokeinterface e : ()Ljava/lang/Float;
    //   67: putfield e : Ljava/lang/Float;
    //   70: aload_1
    //   71: invokeinterface i : ()Ljava/lang/Float;
    //   76: astore_1
    //   77: aload_0
    //   78: aload_1
    //   79: putfield f : Ljava/lang/Float;
    //   82: goto -> 127
    //   85: aload_0
    //   86: aload #4
    //   88: putfield e : Ljava/lang/Float;
    //   91: aload_1
    //   92: invokeinterface h : ()Ljava/lang/Float;
    //   97: astore_3
    //   98: aload_3
    //   99: ifnull -> 119
    //   102: aload_3
    //   103: astore_1
    //   104: aload_3
    //   105: invokevirtual floatValue : ()F
    //   108: aload_0
    //   109: getfield e : Ljava/lang/Float;
    //   112: invokevirtual floatValue : ()F
    //   115: fcmpg
    //   116: ifge -> 77
    //   119: aload_0
    //   120: getfield e : Ljava/lang/Float;
    //   123: astore_1
    //   124: goto -> 77
    //   127: aload_0
    //   128: getfield f : Ljava/lang/Float;
    //   131: invokevirtual floatValue : ()F
    //   134: aload_0
    //   135: getfield e : Ljava/lang/Float;
    //   138: invokevirtual floatValue : ()F
    //   141: invokestatic compare : (FF)I
    //   144: ifle -> 149
    //   147: iconst_1
    //   148: istore_2
    //   149: aload_0
    //   150: iload_2
    //   151: putfield b : Z
    //   154: return
  }
  
  public void a(CaptureRequest.Builder paramBuilder) {
    if (!b())
      return; 
    if (y1.g()) {
      paramBuilder.set(x.a.a(), b.a(this.d.floatValue(), this.e.floatValue(), this.f.floatValue()));
    } else {
      Rect rect = b.b(this.d.floatValue(), this.c, this.e.floatValue(), this.f.floatValue());
      paramBuilder.set(CaptureRequest.SCALER_CROP_REGION, rect);
    } 
  }
  
  public boolean b() {
    return this.b;
  }
  
  public float c() {
    return this.f.floatValue();
  }
  
  public float d() {
    return this.e.floatValue();
  }
  
  public void e(Float paramFloat) {
    this.d = paramFloat;
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ag\a.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */