package ae;

import xg.m;

public final class d {
  public final String a;
  
  public final String b;
  
  public final String c;
  
  public final String d;
  
  public d(String paramString1, String paramString2, String paramString3, String paramString4) {
    this.a = paramString1;
    this.b = paramString2;
    this.c = paramString3;
    this.d = paramString4;
  }
  
  public final String a() {
    return this.d;
  }
  
  public final String b() {
    return this.c;
  }
  
  public final String c() {
    return this.b;
  }
  
  public final String d() {
    return this.a;
  }
  
  public boolean equals(Object paramObject) {
    if (this == paramObject)
      return true; 
    if (!(paramObject instanceof d))
      return false; 
    paramObject = paramObject;
    return !m.a(this.a, ((d)paramObject).a) ? false : (!m.a(this.b, ((d)paramObject).b) ? false : (!m.a(this.c, ((d)paramObject).c) ? false : (!!m.a(this.d, ((d)paramObject).d))));
  }
  
  public int hashCode() {
    int i;
    int k = this.a.hashCode();
    int m = this.b.hashCode();
    int j = this.c.hashCode();
    String str = this.d;
    if (str == null) {
      i = 0;
    } else {
      i = str.hashCode();
    } 
    return ((k * 31 + m) * 31 + j) * 31 + i;
  }
  
  public String toString() {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("NotificationIconData(resType=");
    stringBuilder.append(this.a);
    stringBuilder.append(", resPrefix=");
    stringBuilder.append(this.b);
    stringBuilder.append(", name=");
    stringBuilder.append(this.c);
    stringBuilder.append(", backgroundColorRgb=");
    stringBuilder.append(this.d);
    stringBuilder.append(')');
    return stringBuilder.toString();
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ae\d.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */