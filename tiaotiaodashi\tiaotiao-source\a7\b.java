package a7;

import android.content.Context;
import android.net.Uri;
import com.bumptech.glide.load.data.d;
import java.io.InputStream;
import o7.d;
import t6.f;
import t6.h;
import u6.c;
import z6.o;
import z6.p;
import z6.s;

public class b implements o<Uri, InputStream> {
  public final Context a;
  
  public b(Context paramContext) {
    this.a = paramContext.getApplicationContext();
  }
  
  public o.a<InputStream> c(Uri paramUri, int paramInt1, int paramInt2, h paramh) {
    return u6.b.d(paramInt1, paramInt2) ? new o.a((f)new d(paramUri), (d)c.f(this.a, paramUri)) : null;
  }
  
  public boolean d(Uri paramUri) {
    return u6.b.a(paramUri);
  }
  
  public static class a implements p<Uri, InputStream> {
    public final Context a;
    
    public a(Context param1Context) {
      this.a = param1Context;
    }
    
    public o<Uri, InputStream> c(s param1s) {
      return new b(this.a);
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a7\b.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */