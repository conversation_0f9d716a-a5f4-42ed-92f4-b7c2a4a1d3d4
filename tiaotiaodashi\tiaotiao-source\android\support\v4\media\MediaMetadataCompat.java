package android.support.v4.media;

import android.graphics.Bitmap;
import android.media.MediaMetadata;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.support.v4.media.session.MediaSessionCompat;
import java.util.Set;

public final class MediaMetadataCompat implements Parcelable {
  public static final Parcelable.Creator<MediaMetadataCompat> CREATOR;
  
  public static final t0.a<String, Integer> c;
  
  public static final String[] d = new String[] { "android.media.metadata.TITLE", "android.media.metadata.ARTIST", "android.media.metadata.ALBUM", "android.media.metadata.ALBUM_ARTIST", "android.media.metadata.WRITER", "android.media.metadata.AUTHOR", "android.media.metadata.COMPOSER" };
  
  public static final String[] e = new String[] { "android.media.metadata.DISPLAY_ICON", "android.media.metadata.ART", "android.media.metadata.ALBUM_ART" };
  
  public static final String[] f = new String[] { "android.media.metadata.DISPLAY_ICON_URI", "android.media.metadata.ART_URI", "android.media.metadata.ALBUM_ART_URI" };
  
  public final Bundle a;
  
  public MediaMetadata b;
  
  static {
    CREATOR = new a();
  }
  
  public MediaMetadataCompat(Bundle paramBundle) {
    paramBundle = new Bundle(paramBundle);
    this.a = paramBundle;
    MediaSessionCompat.a(paramBundle);
  }
  
  public MediaMetadataCompat(Parcel paramParcel) {
    this.a = paramParcel.readBundle(MediaSessionCompat.class.getClassLoader());
  }
  
  public static MediaMetadataCompat d(Object paramObject) {
    if (paramObject != null) {
      Parcel parcel = Parcel.obtain();
      paramObject = paramObject;
      paramObject.writeToParcel(parcel, 0);
      parcel.setDataPosition(0);
      MediaMetadataCompat mediaMetadataCompat = (MediaMetadataCompat)CREATOR.createFromParcel(parcel);
      parcel.recycle();
      mediaMetadataCompat.b = (MediaMetadata)paramObject;
      return mediaMetadataCompat;
    } 
    return null;
  }
  
  public boolean c(String paramString) {
    return this.a.containsKey(paramString);
  }
  
  public int describeContents() {
    return 0;
  }
  
  public Bundle g() {
    return new Bundle(this.a);
  }
  
  public long h(String paramString) {
    return this.a.getLong(paramString, 0L);
  }
  
  public Object i() {
    if (this.b == null) {
      Parcel parcel = Parcel.obtain();
      writeToParcel(parcel, 0);
      parcel.setDataPosition(0);
      this.b = (MediaMetadata)MediaMetadata.CREATOR.createFromParcel(parcel);
      parcel.recycle();
    } 
    return this.b;
  }
  
  public Set<String> j() {
    return this.a.keySet();
  }
  
  public int k() {
    return this.a.size();
  }
  
  public void writeToParcel(Parcel paramParcel, int paramInt) {
    paramParcel.writeBundle(this.a);
  }
  
  static {
    t0.a<String, Integer> a1 = new t0.a();
    c = a1;
    Integer integer4 = Integer.valueOf(1);
    a1.put("android.media.metadata.TITLE", integer4);
    a1.put("android.media.metadata.ARTIST", integer4);
    Integer integer2 = Integer.valueOf(0);
    a1.put("android.media.metadata.DURATION", integer2);
    a1.put("android.media.metadata.ALBUM", integer4);
    a1.put("android.media.metadata.AUTHOR", integer4);
    a1.put("android.media.metadata.WRITER", integer4);
    a1.put("android.media.metadata.COMPOSER", integer4);
    a1.put("android.media.metadata.COMPILATION", integer4);
    a1.put("android.media.metadata.DATE", integer4);
    a1.put("android.media.metadata.YEAR", integer2);
    a1.put("android.media.metadata.GENRE", integer4);
    a1.put("android.media.metadata.TRACK_NUMBER", integer2);
    a1.put("android.media.metadata.NUM_TRACKS", integer2);
    a1.put("android.media.metadata.DISC_NUMBER", integer2);
    a1.put("android.media.metadata.ALBUM_ARTIST", integer4);
    Integer integer1 = Integer.valueOf(2);
    a1.put("android.media.metadata.ART", integer1);
    a1.put("android.media.metadata.ART_URI", integer4);
    a1.put("android.media.metadata.ALBUM_ART", integer1);
    a1.put("android.media.metadata.ALBUM_ART_URI", integer4);
    Integer integer3 = Integer.valueOf(3);
    a1.put("android.media.metadata.USER_RATING", integer3);
    a1.put("android.media.metadata.RATING", integer3);
    a1.put("android.media.metadata.DISPLAY_TITLE", integer4);
    a1.put("android.media.metadata.DISPLAY_SUBTITLE", integer4);
    a1.put("android.media.metadata.DISPLAY_DESCRIPTION", integer4);
    a1.put("android.media.metadata.DISPLAY_ICON", integer1);
    a1.put("android.media.metadata.DISPLAY_ICON_URI", integer4);
    a1.put("android.media.metadata.MEDIA_ID", integer4);
    a1.put("android.media.metadata.BT_FOLDER_TYPE", integer2);
    a1.put("android.media.metadata.MEDIA_URI", integer4);
    a1.put("android.media.metadata.ADVERTISEMENT", integer2);
    a1.put("android.media.metadata.DOWNLOAD_STATUS", integer2);
  }
  
  public class a implements Parcelable.Creator<MediaMetadataCompat> {
    public MediaMetadataCompat a(Parcel param1Parcel) {
      return new MediaMetadataCompat(param1Parcel);
    }
    
    public MediaMetadataCompat[] b(int param1Int) {
      return new MediaMetadataCompat[param1Int];
    }
  }
  
  public static final class b {
    public final Bundle a = new Bundle();
    
    public MediaMetadataCompat a() {
      return new MediaMetadataCompat(this.a);
    }
    
    public b b(String param1String, Bitmap param1Bitmap) {
      t0.a<String, Integer> a = MediaMetadataCompat.c;
      if (!a.containsKey(param1String) || ((Integer)a.get(param1String)).intValue() == 2) {
        this.a.putParcelable(param1String, (Parcelable)param1Bitmap);
        return this;
      } 
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("The ");
      stringBuilder.append(param1String);
      stringBuilder.append(" key cannot be used to put a Bitmap");
      throw new IllegalArgumentException(stringBuilder.toString());
    }
    
    public b c(String param1String, long param1Long) {
      t0.a<String, Integer> a = MediaMetadataCompat.c;
      if (!a.containsKey(param1String) || ((Integer)a.get(param1String)).intValue() == 0) {
        this.a.putLong(param1String, param1Long);
        return this;
      } 
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("The ");
      stringBuilder.append(param1String);
      stringBuilder.append(" key cannot be used to put a long");
      throw new IllegalArgumentException(stringBuilder.toString());
    }
    
    public b d(String param1String, RatingCompat param1RatingCompat) {
      t0.a<String, Integer> a = MediaMetadataCompat.c;
      if (!a.containsKey(param1String) || ((Integer)a.get(param1String)).intValue() == 3) {
        this.a.putParcelable(param1String, (Parcelable)param1RatingCompat.g());
        return this;
      } 
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("The ");
      stringBuilder.append(param1String);
      stringBuilder.append(" key cannot be used to put a Rating");
      throw new IllegalArgumentException(stringBuilder.toString());
    }
    
    public b e(String param1String1, String param1String2) {
      t0.a<String, Integer> a = MediaMetadataCompat.c;
      if (!a.containsKey(param1String1) || ((Integer)a.get(param1String1)).intValue() == 1) {
        this.a.putCharSequence(param1String1, param1String2);
        return this;
      } 
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("The ");
      stringBuilder.append(param1String1);
      stringBuilder.append(" key cannot be used to put a String");
      throw new IllegalArgumentException(stringBuilder.toString());
    }
    
    public b f(String param1String, CharSequence param1CharSequence) {
      t0.a<String, Integer> a = MediaMetadataCompat.c;
      if (!a.containsKey(param1String) || ((Integer)a.get(param1String)).intValue() == 1) {
        this.a.putCharSequence(param1String, param1CharSequence);
        return this;
      } 
      param1CharSequence = new StringBuilder();
      param1CharSequence.append("The ");
      param1CharSequence.append(param1String);
      param1CharSequence.append(" key cannot be used to put a CharSequence");
      throw new IllegalArgumentException(param1CharSequence.toString());
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\android\support\v4\media\MediaMetadataCompat.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */