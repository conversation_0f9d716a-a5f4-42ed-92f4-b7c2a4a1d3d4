package a3;

import android.graphics.Bitmap;
import java.io.IOException;
import java.nio.ByteBuffer;
import l2.p;
import l2.x;
import l2.y;
import o2.j0;
import r2.e;
import r2.f;
import r2.g;
import r2.h;
import s2.p2;

public final class a extends h<f, f, d> implements c {
  public final b o;
  
  public a(b paramb) {
    super(new f[1], (g[])new f[1]);
    this.o = paramb;
  }
  
  public static Bitmap C(byte[] paramArrayOfbyte, int paramInt) {
    try {
      return q2.c.a(paramArrayOfbyte, paramInt, null);
    } catch (y y) {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("Could not decode image data with BitmapFactory. (data.length = ");
      stringBuilder.append(paramArrayOfbyte.length);
      stringBuilder.append(", input length = ");
      stringBuilder.append(paramInt);
      stringBuilder.append(")");
      throw new d(stringBuilder.toString(), y);
    } catch (IOException iOException) {
      throw new d(iOException);
    } 
  }
  
  public d A(Throwable paramThrowable) {
    return new d("Unexpected decode error", paramThrowable);
  }
  
  public d B(f paramf, f paramf1, boolean paramBoolean) {
    try {
      ByteBuffer byteBuffer = (ByteBuffer)o2.a.e(paramf.d);
      o2.a.g(byteBuffer.hasArray());
      if (byteBuffer.arrayOffset() == 0) {
        paramBoolean = true;
      } else {
        paramBoolean = false;
      } 
      o2.a.a(paramBoolean);
      paramf1.e = this.o.a(byteBuffer.array(), byteBuffer.remaining());
      paramf1.b = paramf.f;
      return null;
    } catch (d null) {
      return null;
    } 
  }
  
  public f i() {
    return new f(1);
  }
  
  public f z() {
    return new a(this);
  }
  
  public class a extends f {
    public final a f;
    
    public a(a this$0) {}
    
    public void t() {
      a.y(this.f, this);
    }
  }
  
  public static interface b {
    Bitmap a(byte[] param1ArrayOfbyte, int param1Int);
  }
  
  public static final class c implements c.a {
    public final a.b b = new b();
    
    public a b() {
      return new a(this.b, null);
    }
    
    public int c(p param1p) {
      boolean bool;
      String str = param1p.n;
      if (str == null || !x.p(str))
        return p2.u(0); 
      if (j0.z0(param1p.n)) {
        bool = true;
      } else {
        bool = true;
      } 
      return p2.u(bool);
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a3\a.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */