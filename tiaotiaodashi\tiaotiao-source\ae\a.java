package ae;

import android.content.Context;
import android.content.SharedPreferences;
import xg.g;
import xg.m;

public final class a {
  public static final a b = new a(null);
  
  public final String a;
  
  public a(String paramString) {
    this.a = paramString;
  }
  
  public final String a() {
    return this.a;
  }
  
  public boolean equals(Object paramObject) {
    if (this == paramObject)
      return true; 
    if (!(paramObject instanceof a))
      return false; 
    paramObject = paramObject;
    return !!m.a(this.a, ((a)paramObject).a);
  }
  
  public int hashCode() {
    return this.a.hashCode();
  }
  
  public String toString() {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("ForegroundServiceStatus(action=");
    stringBuilder.append(this.a);
    stringBuilder.append(')');
    return stringBuilder.toString();
  }
  
  public static final class a {
    public a() {}
    
    public final a a(Context param1Context) {
      m.e(param1Context, "context");
      String str2 = param1Context.getSharedPreferences("com.pravera.flutter_foreground_task.prefs.FOREGROUND_SERVICE_STATUS", 0).getString("foregroundServiceAction", null);
      String str1 = str2;
      if (str2 == null)
        str1 = "com.pravera.flutter_foreground_task.action.stop"; 
      return new a(str1);
    }
    
    public final void b(Context param1Context, String param1String) {
      m.e(param1Context, "context");
      m.e(param1String, "action");
      SharedPreferences.Editor editor = param1Context.getSharedPreferences("com.pravera.flutter_foreground_task.prefs.FOREGROUND_SERVICE_STATUS", 0).edit();
      editor.putString("foregroundServiceAction", param1String);
      editor.commit();
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\ae\a.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */