package a1;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Typeface;
import android.os.CancellationSignal;
import g1.g;
import java.io.File;
import java.io.InputStream;
import java.util.concurrent.ConcurrentHashMap;
import z0.d;

public class c0 {
  public ConcurrentHashMap<Long, d.c> a = new ConcurrentHashMap<Long, d.c>();
  
  public static <T> T e(T[] paramArrayOfT, int paramInt, b<T> paramb) {
    char c;
    boolean bool;
    if ((paramInt & 0x1) == 0) {
      c = 'Ɛ';
    } else {
      c = 'ʼ';
    } 
    if ((paramInt & 0x2) != 0) {
      bool = true;
    } else {
      bool = false;
    } 
    return f(paramArrayOfT, c, bool, paramb);
  }
  
  public static <T> T f(T[] paramArrayOfT, int paramInt, boolean paramBoolean, b<T> paramb) {
    // Byte code:
    //   0: aload_0
    //   1: arraylength
    //   2: istore #8
    //   4: aconst_null
    //   5: astore #9
    //   7: ldc 2147483647
    //   9: istore #5
    //   11: iconst_0
    //   12: istore #4
    //   14: iload #4
    //   16: iload #8
    //   18: if_icmpge -> 106
    //   21: aload_0
    //   22: iload #4
    //   24: aaload
    //   25: astore #10
    //   27: aload_3
    //   28: aload #10
    //   30: invokeinterface a : (Ljava/lang/Object;)I
    //   35: iload_1
    //   36: isub
    //   37: invokestatic abs : (I)I
    //   40: istore #7
    //   42: aload_3
    //   43: aload #10
    //   45: invokeinterface b : (Ljava/lang/Object;)Z
    //   50: iload_2
    //   51: if_icmpne -> 60
    //   54: iconst_0
    //   55: istore #6
    //   57: goto -> 63
    //   60: iconst_1
    //   61: istore #6
    //   63: iload #7
    //   65: iconst_2
    //   66: imul
    //   67: iload #6
    //   69: iadd
    //   70: istore #7
    //   72: aload #9
    //   74: ifnull -> 88
    //   77: iload #5
    //   79: istore #6
    //   81: iload #5
    //   83: iload #7
    //   85: if_icmple -> 96
    //   88: aload #10
    //   90: astore #9
    //   92: iload #7
    //   94: istore #6
    //   96: iinc #4, 1
    //   99: iload #6
    //   101: istore #5
    //   103: goto -> 14
    //   106: aload #9
    //   108: areturn
  }
  
  public Typeface a(Context paramContext, d.c paramc, Resources paramResources, int paramInt) {
    throw null;
  }
  
  public Typeface b(Context paramContext, CancellationSignal paramCancellationSignal, g.b[] paramArrayOfb, int paramInt) {
    throw null;
  }
  
  public Typeface c(Context paramContext, InputStream paramInputStream) {
    File file = d0.e(paramContext);
    if (file == null)
      return null; 
    try {
      boolean bool = d0.d(file, paramInputStream);
      if (!bool)
        return null; 
      return Typeface.createFromFile(file.getPath());
    } catch (RuntimeException runtimeException) {
      return null;
    } finally {
      file.delete();
    } 
  }
  
  public Typeface d(Context paramContext, Resources paramResources, int paramInt1, String paramString, int paramInt2) {
    File file = d0.e(paramContext);
    if (file == null)
      return null; 
    try {
      boolean bool = d0.c(file, paramResources, paramInt1);
      if (!bool)
        return null; 
      return Typeface.createFromFile(file.getPath());
    } catch (RuntimeException runtimeException) {
      return null;
    } finally {
      file.delete();
    } 
  }
  
  public g.b g(g.b[] paramArrayOfb, int paramInt) {
    return e(paramArrayOfb, paramInt, new a(this));
  }
  
  public class a implements b<g.b> {
    public final c0 a;
    
    public a(c0 this$0) {}
    
    public int c(g.b param1b) {
      return param1b.e();
    }
    
    public boolean d(g.b param1b) {
      return param1b.f();
    }
  }
  
  public static interface b<T> {
    int a(T param1T);
    
    boolean b(T param1T);
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a1\c0.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */