package a5;

public enum d {
  AGGREGATION_COUNT,
  CLASSES,
  DEX_FILES(0L),
  EXTRA_DESCRIPTORS(1L),
  METHODS(1L);
  
  private static final d[] $VALUES;
  
  private final long mValue;
  
  static {
    CLASSES = new d("CLASSES", 2, 2L);
    METHODS = new d("METHODS", 3, 3L);
    AGGREGATION_COUNT = new d("AGGREGATION_COUNT", 4, 4L);
    $VALUES = a();
  }
  
  d(long paramLong) {
    this.mValue = paramLong;
  }
  
  public long b() {
    return this.mValue;
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a5\d.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */