package aa;

import b9.e;
import b9.y;
import ta.k;
import ta.n;
import ta.q;
import ta.r;
import u8.q1;

public final class p extends a {
  public final int o;
  
  public final q1 p;
  
  public long q;
  
  public boolean r;
  
  public p(n paramn, r paramr, q1 paramq11, int paramInt1, Object paramObject, long paramLong1, long paramLong2, long paramLong3, int paramInt2, q1 paramq12) {
    super(paramn, paramr, paramq11, paramInt1, paramObject, paramLong1, paramLong2, -9223372036854775807L, -9223372036854775807L, paramLong3);
    this.o = paramInt2;
    this.p = paramq12;
  }
  
  public void a() {
    c c = j();
    c.c(0L);
    int j = this.o;
    int i = 0;
    null = c.b(0, j);
    null.a(this.p);
    try {
      r r = this.b.e(this.q);
      long l2 = this.i.e(r);
      long l1 = l2;
      if (l2 != -1L)
        l1 = l2 + this.q; 
      e e = new e();
      this((k)this.i, this.q, l1);
      while (i != -1) {
        this.q += i;
        i = null.c((k)e, 2147483647, true);
      } 
      i = (int)this.q;
      null.d(this.g, 1, i, 0, null);
      q.a((n)this.i);
      return;
    } finally {
      q.a((n)this.i);
    } 
  }
  
  public void b() {}
  
  public boolean h() {
    return this.r;
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\aa\p.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */