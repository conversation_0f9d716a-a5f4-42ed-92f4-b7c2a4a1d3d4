package android.support.v4.media;

import android.os.Bundle;
import android.os.Parcelable;
import android.support.v4.media.session.MediaSessionCompat;
import c.b;

class MediaBrowserCompat$ItemReceiver extends b {
  public void c(int paramInt, Bundle paramBundle) {
    Bundle bundle = paramBundle;
    if (paramBundle != null)
      bundle = MediaSessionCompat.n(paramBundle); 
    if (paramInt == 0 && bundle != null && bundle.containsKey("media_item")) {
      Parcelable parcelable = bundle.getParcelable("media_item");
      if (parcelable == null || parcelable instanceof MediaBrowserCompat$MediaItem) {
        parcelable = parcelable;
        throw null;
      } 
      throw null;
    } 
    throw null;
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\android\support\v4\media\MediaBrowserCompat$ItemReceiver.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */