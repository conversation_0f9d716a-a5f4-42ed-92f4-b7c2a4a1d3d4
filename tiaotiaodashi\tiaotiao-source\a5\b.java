package a5;

import java.util.TreeMap;

public class b {
  public final String a;
  
  public final String b;
  
  public final long c;
  
  public long d;
  
  public int e;
  
  public final int f;
  
  public final int g;
  
  public int[] h;
  
  public final TreeMap<Integer, Integer> i;
  
  public b(String paramString1, String paramString2, long paramLong1, long paramLong2, int paramInt1, int paramInt2, int paramInt3, int[] paramArrayOfint, TreeMap<Integer, Integer> paramTreeMap) {
    this.a = paramString1;
    this.b = paramString2;
    this.c = paramLong1;
    this.d = paramLong2;
    this.e = paramInt1;
    this.f = paramInt2;
    this.g = paramInt3;
    this.h = paramArrayOfint;
    this.i = paramTreeMap;
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a5\b.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */