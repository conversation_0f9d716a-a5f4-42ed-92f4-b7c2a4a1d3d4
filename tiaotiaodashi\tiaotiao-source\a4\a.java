package a4;

import android.os.Parcel;
import android.os.Parcelable;
import l2.w;

public final class a implements w.b {
  public static final Parcelable.Creator<a> CREATOR = new a();
  
  public final int a;
  
  public final String b;
  
  public a(int paramInt, String paramString) {
    this.a = paramInt;
    this.b = paramString;
  }
  
  public int describeContents() {
    return 0;
  }
  
  public String toString() {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("Ait(controlCode=");
    stringBuilder.append(this.a);
    stringBuilder.append(",url=");
    stringBuilder.append(this.b);
    stringBuilder.append(")");
    return stringBuilder.toString();
  }
  
  public void writeToParcel(Parcel paramParcel, int paramInt) {
    paramParcel.writeString(this.b);
    paramParcel.writeInt(this.a);
  }
  
  public class a implements Parcelable.Creator<a> {
    public a a(Parcel param1Parcel) {
      String str = (String)o2.a.e(param1Parcel.readString());
      return new a(param1Parcel.readInt(), str);
    }
    
    public a[] b(int param1Int) {
      return new a[param1Int];
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a4\a.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */