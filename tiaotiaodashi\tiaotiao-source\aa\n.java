package aa;

import ta.r;
import u8.q1;
import va.a;

public abstract class n extends f {
  public final long j;
  
  public n(ta.n paramn, r paramr, q1 paramq1, int paramInt, Object paramObject, long paramLong1, long paramLong2, long paramLong3) {
    super(paramn, paramr, 1, paramq1, paramInt, paramObject, paramLong1, paramLong2);
    a.e(paramq1);
    this.j = paramLong3;
  }
  
  public long g() {
    long l2 = this.j;
    long l1 = -1L;
    if (l2 != -1L)
      l1 = 1L + l2; 
    return l1;
  }
  
  public abstract boolean h();
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\aa\n.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */