package a9;

import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.ResultReceiver;
import android.os.SystemClock;
import android.support.v4.media.MediaDescriptionCompat;
import android.support.v4.media.MediaMetadataCompat;
import android.support.v4.media.RatingCompat;
import android.support.v4.media.session.MediaControllerCompat;
import android.support.v4.media.session.MediaSessionCompat;
import android.support.v4.media.session.PlaybackStateCompat;
import android.util.Pair;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import u8.n1;
import u8.p2;
import u8.t2;
import u8.z1;
import va.p0;

public final class a {
  public static final MediaMetadataCompat q = (new MediaMetadataCompat.b()).a();
  
  public final MediaSessionCompat a;
  
  public final Looper b;
  
  public final d c;
  
  public final ArrayList<c> d;
  
  public final ArrayList<c> e;
  
  public e[] f;
  
  public Map<String, e> g;
  
  public h h;
  
  public t2 i;
  
  public Pair<Integer, CharSequence> j;
  
  public Bundle k;
  
  public long l;
  
  public boolean m;
  
  public boolean n;
  
  public boolean o;
  
  public boolean p;
  
  public a(MediaSessionCompat paramMediaSessionCompat) {
    this.a = paramMediaSessionCompat;
    Looper looper = p0.Q();
    this.b = looper;
    d d1 = new d(null);
    this.c = d1;
    this.d = new ArrayList<c>();
    this.e = new ArrayList<c>();
    this.f = new e[0];
    this.g = Collections.emptyMap();
    this.h = new f(paramMediaSessionCompat.b(), null);
    this.l = 2360143L;
    paramMediaSessionCompat.i(3);
    paramMediaSessionCompat.h(d1, new Handler(looper));
    this.o = true;
  }
  
  public final boolean A() {
    return false;
  }
  
  public final boolean B(long paramLong) {
    return false;
  }
  
  public final boolean C(long paramLong) {
    return false;
  }
  
  public final int D(int paramInt, boolean paramBoolean) {
    byte b = 2;
    if (paramInt != 2) {
      if (paramInt != 3)
        return (paramInt != 4) ? this.p : 1; 
      if (paramBoolean)
        b = 3; 
      return b;
    } 
    if (paramBoolean)
      b = 6; 
    return b;
  }
  
  public final void E() {
    // Byte code:
    //   0: aload_0
    //   1: getfield h : La9/a$h;
    //   4: astore_1
    //   5: aload_1
    //   6: ifnull -> 29
    //   9: aload_0
    //   10: getfield i : Lu8/t2;
    //   13: astore_2
    //   14: aload_2
    //   15: ifnull -> 29
    //   18: aload_1
    //   19: aload_2
    //   20: invokeinterface b : (Lu8/t2;)Landroid/support/v4/media/MediaMetadataCompat;
    //   25: astore_1
    //   26: goto -> 33
    //   29: getstatic a9/a.q : Landroid/support/v4/media/MediaMetadataCompat;
    //   32: astore_1
    //   33: aload_0
    //   34: getfield h : La9/a$h;
    //   37: astore_2
    //   38: aload_0
    //   39: getfield m : Z
    //   42: ifeq -> 76
    //   45: aload_2
    //   46: ifnull -> 76
    //   49: aload_0
    //   50: getfield a : Landroid/support/v4/media/session/MediaSessionCompat;
    //   53: invokevirtual b : ()Landroid/support/v4/media/session/MediaControllerCompat;
    //   56: invokevirtual a : ()Landroid/support/v4/media/MediaMetadataCompat;
    //   59: astore_3
    //   60: aload_3
    //   61: ifnull -> 76
    //   64: aload_2
    //   65: aload_3
    //   66: aload_1
    //   67: invokeinterface a : (Landroid/support/v4/media/MediaMetadataCompat;Landroid/support/v4/media/MediaMetadataCompat;)Z
    //   72: ifeq -> 76
    //   75: return
    //   76: aload_0
    //   77: getfield a : Landroid/support/v4/media/session/MediaSessionCompat;
    //   80: aload_1
    //   81: invokevirtual j : (Landroid/support/v4/media/MediaMetadataCompat;)V
    //   84: return
  }
  
  public final void F() {
    PlaybackStateCompat.d d1 = new PlaybackStateCompat.d();
    t2 t21 = this.i;
    boolean bool = false;
    if (t21 == null) {
      d1.c(v()).i(0, 0L, 0.0F, SystemClock.elapsedRealtime());
      this.a.l(0);
      this.a.m(0);
    } else {
      int i;
      HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
      for (e e1 : this.f) {
        PlaybackStateCompat.CustomAction customAction = e1.b(t21);
        if (customAction != null) {
          hashMap.put(customAction.d(), e1);
          d1.a(customAction);
        } 
      } 
      this.g = Collections.unmodifiableMap(hashMap);
      Bundle bundle = new Bundle();
      p2 p2 = t21.v();
      byte b = 1;
      if (p2 != null || this.j != null) {
        i = 1;
      } else {
        i = 0;
      } 
      if (i) {
        i = 7;
      } else {
        i = D(t21.g(), t21.r());
      } 
      Pair<Integer, CharSequence> pair = this.j;
      if (pair != null) {
        d1.f(((Integer)pair.first).intValue(), (CharSequence)this.j.second);
        Bundle bundle1 = this.k;
        if (bundle1 != null)
          bundle.putAll(bundle1); 
      } 
      float f = (t21.f()).a;
      bundle.putFloat("EXO_SPEED", f);
      if (!t21.T())
        f = 0.0F; 
      z1 z1 = t21.L();
      if (z1 != null && !"".equals(z1.a))
        bundle.putString("androidx.media.PlaybackStateCompat.Extras.KEY_MEDIA_ID", z1.a); 
      d1.c(v() | u(t21)).d(-1L).e(t21.y()).i(i, t21.n(), f, SystemClock.elapsedRealtime()).g(bundle);
      int j = t21.m();
      MediaSessionCompat mediaSessionCompat = this.a;
      if (j == 1) {
        i = b;
      } else {
        b = 2;
        i = bool;
        if (j == 2)
          i = b; 
      } 
      mediaSessionCompat.l(i);
      this.a.m(t21.G());
    } 
    this.a.k(d1.b());
  }
  
  public final void G() {}
  
  public final void H(t2 paramt2, int paramInt, long paramLong) {
    paramt2.I(paramInt, paramLong);
  }
  
  public void I(t2 paramt2) {
    boolean bool;
    if (paramt2 == null || paramt2.W() == this.b) {
      bool = true;
    } else {
      bool = false;
    } 
    va.a.a(bool);
    t2 t21 = this.i;
    if (t21 != null)
      t21.c0(this.c); 
    this.i = paramt2;
    if (paramt2 != null)
      paramt2.V(this.c); 
    F();
    E();
  }
  
  public final long u(t2 paramt2) {
    boolean bool3 = paramt2.U(5);
    boolean bool2 = paramt2.U(11);
    boolean bool1 = paramt2.U(12);
    if (!paramt2.F().u())
      paramt2.p(); 
    if (bool3) {
      l2 = 6554375L;
    } else {
      l2 = 6554119L;
    } 
    long l1 = l2;
    if (bool1)
      l1 = l2 | 0x40L; 
    long l2 = l1;
    if (bool2)
      l2 = l1 | 0x8L; 
    return this.l & l2;
  }
  
  public final long v() {
    return 0L;
  }
  
  public final boolean w() {
    return false;
  }
  
  public final boolean x(long paramLong) {
    boolean bool;
    if (this.i != null && ((paramLong & this.l) != 0L || this.n)) {
      bool = true;
    } else {
      bool = false;
    } 
    return bool;
  }
  
  public final boolean y() {
    return false;
  }
  
  public final boolean z() {
    return false;
  }
  
  static {
    n1.a("goog.exo.mediasession");
  }
  
  public static interface b extends c {}
  
  public static interface c {
    boolean a(t2 param1t2, String param1String, Bundle param1Bundle, ResultReceiver param1ResultReceiver);
  }
  
  public class d extends MediaSessionCompat.b implements t2.d {
    public int f;
    
    public int g;
    
    public final a h;
    
    public d(a this$0) {}
    
    public void A0() {
      if (!a.s(this.h, 32L))
        return; 
      a.l(this.h);
      a.o(this.h);
      throw null;
    }
    
    public void B0() {
      if (!a.s(this.h, 16L))
        return; 
      a.l(this.h);
      a.o(this.h);
      throw null;
    }
    
    public void C0(long param1Long) {
      if (!a.s(this.h, 4096L))
        return; 
      a.l(this.h);
      a.o(this.h);
      throw null;
    }
    
    public void D0() {
      if (a.n(this.h, 1L)) {
        a.o(this.h).stop();
        if (a.r(this.h))
          a.o(this.h).K(); 
      } 
    }
    
    public void Q(MediaDescriptionCompat param1MediaDescriptionCompat) {
      if (!a.g(this.h))
        return; 
      a.h(this.h);
      a.o(this.h);
      throw null;
    }
    
    public void R(MediaDescriptionCompat param1MediaDescriptionCompat, int param1Int) {
      if (!a.g(this.h))
        return; 
      a.h(this.h);
      a.o(this.h);
      throw null;
    }
    
    public void W(String param1String, Bundle param1Bundle, ResultReceiver param1ResultReceiver) {
      if (a.o(this.h) != null) {
        byte b2;
        byte b3 = 0;
        byte b1 = 0;
        while (true) {
          b2 = b3;
          if (b1 < a.b(this.h).size()) {
            if (((a.c)a.b(this.h).get(b1)).a(a.o(this.h), param1String, param1Bundle, param1ResultReceiver))
              return; 
            b1++;
            continue;
          } 
          break;
        } 
        while (b2 < a.c(this.h).size()) {
          if (((a.c)a.c(this.h).get(b2)).a(a.o(this.h), param1String, param1Bundle, param1ResultReceiver))
            return; 
          b2++;
        } 
      } 
    }
    
    public void Y(t2 param1t2, t2.c param1c) {
      byte b1;
      boolean bool1 = param1c.a(11);
      boolean bool = true;
      if (bool1) {
        if (this.f != param1t2.C()) {
          a.l(this.h);
          i = 1;
        } else {
          i = 0;
        } 
        b1 = 1;
      } else {
        i = 0;
        b1 = 0;
      } 
      int j = i;
      int i = b1;
      if (param1c.a(0)) {
        i = param1t2.F().t();
        j = param1t2.C();
        a.l(this.h);
        if (this.g != i || this.f != j)
          b1 = 1; 
        this.g = i;
        j = 1;
        i = b1;
      } 
      this.f = param1t2.C();
      if (param1c.b(new int[] { 4, 5, 7, 8, 12 }))
        i = 1; 
      if (param1c.b(new int[] { 9 })) {
        this.h.G();
        i = bool;
      } 
      if (i != 0)
        this.h.F(); 
      if (j != 0)
        this.h.E(); 
    }
    
    public void a0(String param1String, Bundle param1Bundle) {
      if (a.o(this.h) != null && a.t(this.h).containsKey(param1String)) {
        ((a.e)a.t(this.h).get(param1String)).a(a.o(this.h), param1String, param1Bundle);
        this.h.F();
      } 
    }
    
    public void b0() {
      if (a.n(this.h, 64L))
        a.o(this.h).Y(); 
    }
    
    public boolean c0(Intent param1Intent) {
      if (!a.k(this.h)) {
        boolean bool;
        if (super.c0(param1Intent)) {
          bool = true;
        } else {
          bool = false;
        } 
        return bool;
      } 
      a.m(this.h);
      a.o(this.h);
      throw null;
    }
    
    public void e0() {
      if (a.n(this.h, 2L))
        a.o(this.h).d(); 
    }
    
    public void f0() {
      if (a.n(this.h, 4L)) {
        if (a.o(this.h).g() == 1) {
          a.p(this.h);
          a.o(this.h).e();
        } else if (a.o(this.h).g() == 4) {
          a a1 = this.h;
          a.q(a1, a.o(a1), a.o(this.h).C(), -9223372036854775807L);
        } 
        ((t2)va.a.e(a.o(this.h))).h();
      } 
    }
    
    public void g0(String param1String, Bundle param1Bundle) {
      if (!a.d(this.h, 1024L))
        return; 
      a.p(this.h);
      throw null;
    }
    
    public void l0(String param1String, Bundle param1Bundle) {
      if (!a.d(this.h, 2048L))
        return; 
      a.p(this.h);
      throw null;
    }
    
    public void m0(Uri param1Uri, Bundle param1Bundle) {
      if (!a.d(this.h, 8192L))
        return; 
      a.p(this.h);
      throw null;
    }
    
    public void n0() {
      if (!a.d(this.h, 16384L))
        return; 
      a.p(this.h);
      throw null;
    }
    
    public void o0(String param1String, Bundle param1Bundle) {
      if (!a.d(this.h, 32768L))
        return; 
      a.p(this.h);
      throw null;
    }
    
    public void p0(String param1String, Bundle param1Bundle) {
      if (!a.d(this.h, 65536L))
        return; 
      a.p(this.h);
      throw null;
    }
    
    public void q0(Uri param1Uri, Bundle param1Bundle) {
      if (!a.d(this.h, 131072L))
        return; 
      a.p(this.h);
      throw null;
    }
    
    public void r0(MediaDescriptionCompat param1MediaDescriptionCompat) {
      if (!a.g(this.h))
        return; 
      a.h(this.h);
      a.o(this.h);
      throw null;
    }
    
    public void s0() {
      if (a.n(this.h, 8L))
        a.o(this.h).Z(); 
    }
    
    public void t0(long param1Long) {
      if (a.n(this.h, 256L)) {
        a a1 = this.h;
        a.q(a1, a.o(a1), a.o(this.h).C(), param1Long);
      } 
    }
    
    public void u0(boolean param1Boolean) {
      if (!a.i(this.h))
        return; 
      a.j(this.h);
      a.o(this.h);
      throw null;
    }
    
    public void v0(float param1Float) {
      if (a.n(this.h, 4194304L) && param1Float > 0.0F)
        a.o(this.h).k(a.o(this.h).f().e(param1Float)); 
    }
    
    public void w0(RatingCompat param1RatingCompat) {
      if (!a.e(this.h))
        return; 
      a.f(this.h);
      a.o(this.h);
      throw null;
    }
    
    public void x0(RatingCompat param1RatingCompat, Bundle param1Bundle) {
      if (!a.e(this.h))
        return; 
      a.f(this.h);
      a.o(this.h);
      throw null;
    }
    
    public void y0(int param1Int) {
      if (a.n(this.h, 262144L)) {
        byte b1 = 1;
        if (param1Int != 1) {
          byte b2 = 2;
          b1 = b2;
          if (param1Int != 2) {
            b1 = b2;
            if (param1Int != 3)
              b1 = 0; 
          } 
        } 
        a.o(this.h).j(b1);
      } 
    }
    
    public void z0(int param1Int) {
      if (a.n(this.h, 2097152L)) {
        boolean bool2 = true;
        boolean bool1 = bool2;
        if (param1Int != 1) {
          bool1 = bool2;
          if (param1Int != 2)
            bool1 = false; 
        } 
        a.o(this.h).M(bool1);
      } 
    }
  }
  
  public static interface e {
    void a(t2 param1t2, String param1String, Bundle param1Bundle);
    
    PlaybackStateCompat.CustomAction b(t2 param1t2);
  }
  
  public static final class f implements h {
    public final MediaControllerCompat a;
    
    public final String b;
    
    public f(MediaControllerCompat param1MediaControllerCompat, String param1String) {
      this.a = param1MediaControllerCompat;
      if (param1String == null)
        param1String = ""; 
      this.b = param1String;
    }
    
    public MediaMetadataCompat b(t2 param1t2) {
      if (param1t2.F().u())
        return a.a(); 
      MediaMetadataCompat.b b = new MediaMetadataCompat.b();
      if (param1t2.p())
        b.c("android.media.metadata.ADVERTISEMENT", 1L); 
      if (param1t2.D() || param1t2.getDuration() == -9223372036854775807L) {
        l = -1L;
      } else {
        l = param1t2.getDuration();
      } 
      b.c("android.media.metadata.DURATION", l);
      long l = this.a.b().g();
      if (l != -1L) {
        List<MediaSessionCompat.QueueItem> list = this.a.c();
        for (byte b1 = 0; list != null && b1 < list.size(); b1++) {
          MediaSessionCompat.QueueItem queueItem = list.get(b1);
          if (queueItem.h() == l) {
            MediaDescriptionCompat mediaDescriptionCompat = queueItem.g();
            Bundle bundle = mediaDescriptionCompat.g();
            if (bundle != null)
              for (String str1 : bundle.keySet()) {
                Object object = bundle.get(str1);
                if (object instanceof String) {
                  String str2 = String.valueOf(this.b);
                  str1 = String.valueOf(str1);
                  if (str1.length() != 0) {
                    str1 = str2.concat(str1);
                  } else {
                    str1 = new String(str2);
                  } 
                  b.e(str1, (String)object);
                  continue;
                } 
                if (object instanceof CharSequence) {
                  String str2 = String.valueOf(this.b);
                  str1 = String.valueOf(str1);
                  if (str1.length() != 0) {
                    str1 = str2.concat(str1);
                  } else {
                    str1 = new String(str2);
                  } 
                  b.f(str1, (CharSequence)object);
                  continue;
                } 
                if (object instanceof Long) {
                  String str2 = String.valueOf(this.b);
                  str1 = String.valueOf(str1);
                  if (str1.length() != 0) {
                    str1 = str2.concat(str1);
                  } else {
                    str1 = new String(str2);
                  } 
                  l = ((Long)object).longValue();
                } else if (object instanceof Integer) {
                  String str2 = String.valueOf(this.b);
                  str1 = String.valueOf(str1);
                  if (str1.length() != 0) {
                    str1 = str2.concat(str1);
                  } else {
                    str1 = new String(str2);
                  } 
                  l = ((Integer)object).intValue();
                } else {
                  if (object instanceof Bitmap) {
                    String str2 = String.valueOf(this.b);
                    str1 = String.valueOf(str1);
                    if (str1.length() != 0) {
                      str1 = str2.concat(str1);
                    } else {
                      str1 = new String(str2);
                    } 
                    b.b(str1, (Bitmap)object);
                    continue;
                  } 
                  if (object instanceof RatingCompat) {
                    String str2 = String.valueOf(this.b);
                    str1 = String.valueOf(str1);
                    if (str1.length() != 0) {
                      str1 = str2.concat(str1);
                    } else {
                      str1 = new String(str2);
                    } 
                    b.d(str1, (RatingCompat)object);
                  } 
                  continue;
                } 
                b.c(str1, l);
              }  
            CharSequence charSequence = mediaDescriptionCompat.n();
            if (charSequence != null) {
              charSequence = String.valueOf(charSequence);
              b.e("android.media.metadata.TITLE", (String)charSequence);
              b.e("android.media.metadata.DISPLAY_TITLE", (String)charSequence);
            } 
            charSequence = mediaDescriptionCompat.m();
            if (charSequence != null)
              b.e("android.media.metadata.DISPLAY_SUBTITLE", String.valueOf(charSequence)); 
            charSequence = mediaDescriptionCompat.d();
            if (charSequence != null)
              b.e("android.media.metadata.DISPLAY_DESCRIPTION", String.valueOf(charSequence)); 
            Bitmap bitmap = mediaDescriptionCompat.h();
            if (bitmap != null)
              b.b("android.media.metadata.DISPLAY_ICON", bitmap); 
            Uri uri2 = mediaDescriptionCompat.i();
            if (uri2 != null)
              b.e("android.media.metadata.DISPLAY_ICON_URI", String.valueOf(uri2)); 
            String str = mediaDescriptionCompat.k();
            if (str != null)
              b.e("android.media.metadata.MEDIA_ID", str); 
            Uri uri1 = mediaDescriptionCompat.l();
            if (uri1 != null)
              b.e("android.media.metadata.MEDIA_URI", String.valueOf(uri1)); 
            break;
          } 
        } 
      } 
      return b.a();
    }
  }
  
  public static interface g {}
  
  public static interface h {
    default boolean a(MediaMetadataCompat param1MediaMetadataCompat1, MediaMetadataCompat param1MediaMetadataCompat2) {
      if (param1MediaMetadataCompat1 == param1MediaMetadataCompat2)
        return true; 
      if (param1MediaMetadataCompat1.k() != param1MediaMetadataCompat2.k())
        return false; 
      Set set = param1MediaMetadataCompat1.j();
      Bundle bundle1 = param1MediaMetadataCompat1.g();
      Bundle bundle2 = param1MediaMetadataCompat2.g();
      for (String str : set) {
        Object object1 = bundle1.get(str);
        Object object2 = bundle2.get(str);
        if (object1 == object2)
          continue; 
        if (object1 instanceof Bitmap && object2 instanceof Bitmap) {
          if (!((Bitmap)object1).sameAs((Bitmap)object2))
            return false; 
          continue;
        } 
        if (object1 instanceof RatingCompat && object2 instanceof RatingCompat) {
          object1 = object1;
          object2 = object2;
          if (object1.j() != object2.j() || object1.k() != object2.k() || object1.l() != object2.l() || object1.d() != object2.d() || object1.i() != object2.i() || object1.h() != object2.h())
            return false; 
          continue;
        } 
        if (!p0.c(object1, object2))
          return false; 
      } 
      return true;
    }
    
    MediaMetadataCompat b(t2 param1t2);
  }
  
  public static interface i extends c {}
  
  public static interface j extends c {}
  
  public static interface k extends c {}
  
  public static interface l extends c {}
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\a9\a.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */