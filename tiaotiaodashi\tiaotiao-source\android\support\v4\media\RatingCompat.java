package android.support.v4.media;

import android.media.Rating;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.Log;

public final class RatingCompat implements Parcelable {
  public static final Parcelable.Creator<RatingCompat> CREATOR = new a();
  
  public final int a;
  
  public final float b;
  
  public Object c;
  
  public RatingCompat(int paramInt, float paramFloat) {
    this.a = paramInt;
    this.b = paramFloat;
  }
  
  public static RatingCompat c(Object paramObject) {
    RatingCompat ratingCompat;
    Rating rating = null;
    if (paramObject != null) {
      rating = (Rating)paramObject;
      int i = b.b(rating);
      if (b.e(rating)) {
        switch (i) {
          default:
            return null;
          case 6:
            ratingCompat = n(b.a(rating));
            break;
          case 3:
          case 4:
          case 5:
            ratingCompat = o(i, b.c((Rating)ratingCompat));
            break;
          case 2:
            ratingCompat = p(b.f((Rating)ratingCompat));
            break;
          case 1:
            ratingCompat = m(b.d((Rating)ratingCompat));
            break;
        } 
      } else {
        ratingCompat = q(i);
      } 
      ratingCompat.c = paramObject;
    } 
    return ratingCompat;
  }
  
  public static RatingCompat m(boolean paramBoolean) {
    float f;
    if (paramBoolean) {
      f = 1.0F;
    } else {
      f = 0.0F;
    } 
    return new RatingCompat(1, f);
  }
  
  public static RatingCompat n(float paramFloat) {
    if (paramFloat < 0.0F || paramFloat > 100.0F) {
      Log.e("Rating", "Invalid percentage-based rating value");
      return null;
    } 
    return new RatingCompat(6, paramFloat);
  }
  
  public static RatingCompat o(int paramInt, float paramFloat) {
    float f;
    if (paramInt != 3) {
      if (paramInt != 4) {
        if (paramInt != 5) {
          StringBuilder stringBuilder = new StringBuilder();
          stringBuilder.append("Invalid rating style (");
          stringBuilder.append(paramInt);
          stringBuilder.append(") for a star rating");
          String str = stringBuilder.toString();
          Log.e("Rating", str);
          return null;
        } 
        f = 5.0F;
      } else {
        f = 4.0F;
      } 
    } else {
      f = 3.0F;
    } 
    if (paramFloat < 0.0F || paramFloat > f) {
      String str = "Trying to set out of range star-based rating";
      Log.e("Rating", str);
      return null;
    } 
    return new RatingCompat(paramInt, paramFloat);
  }
  
  public static RatingCompat p(boolean paramBoolean) {
    float f;
    if (paramBoolean) {
      f = 1.0F;
    } else {
      f = 0.0F;
    } 
    return new RatingCompat(2, f);
  }
  
  public static RatingCompat q(int paramInt) {
    switch (paramInt) {
      default:
        return null;
      case 1:
      case 2:
      case 3:
      case 4:
      case 5:
      case 6:
        break;
    } 
    return new RatingCompat(paramInt, -1.0F);
  }
  
  public float d() {
    return (this.a != 6 || !k()) ? -1.0F : this.b;
  }
  
  public int describeContents() {
    return this.a;
  }
  
  public Object g() {
    if (this.c == null) {
      Rating rating;
      if (k()) {
        int i = this.a;
        switch (i) {
          default:
            return null;
          case 6:
            rating = b.h(d());
            break;
          case 3:
          case 4:
          case 5:
            rating = b.i(i, i());
            break;
          case 2:
            rating = b.j(l());
            break;
          case 1:
            rating = b.g(j());
            break;
        } 
      } else {
        rating = b.k(this.a);
      } 
      this.c = rating;
    } 
    return this.c;
  }
  
  public int h() {
    return this.a;
  }
  
  public float i() {
    int i = this.a;
    return ((i == 3 || i == 4 || i == 5) && k()) ? this.b : -1.0F;
  }
  
  public boolean j() {
    int i = this.a;
    boolean bool = false;
    if (i != 1)
      return false; 
    if (this.b == 1.0F)
      bool = true; 
    return bool;
  }
  
  public boolean k() {
    boolean bool;
    if (this.b >= 0.0F) {
      bool = true;
    } else {
      bool = false;
    } 
    return bool;
  }
  
  public boolean l() {
    int i = this.a;
    boolean bool = false;
    if (i != 2)
      return false; 
    if (this.b == 1.0F)
      bool = true; 
    return bool;
  }
  
  public String toString() {
    String str;
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("Rating:style=");
    stringBuilder.append(this.a);
    stringBuilder.append(" rating=");
    float f = this.b;
    if (f < 0.0F) {
      str = "unrated";
    } else {
      str = String.valueOf(f);
    } 
    stringBuilder.append(str);
    return stringBuilder.toString();
  }
  
  public void writeToParcel(Parcel paramParcel, int paramInt) {
    paramParcel.writeInt(this.a);
    paramParcel.writeFloat(this.b);
  }
  
  public class a implements Parcelable.Creator<RatingCompat> {
    public RatingCompat a(Parcel param1Parcel) {
      return new RatingCompat(param1Parcel.readInt(), param1Parcel.readFloat());
    }
    
    public RatingCompat[] b(int param1Int) {
      return new RatingCompat[param1Int];
    }
  }
  
  public static class b {
    public static float a(Rating param1Rating) {
      return param1Rating.getPercentRating();
    }
    
    public static int b(Rating param1Rating) {
      return param1Rating.getRatingStyle();
    }
    
    public static float c(Rating param1Rating) {
      return param1Rating.getStarRating();
    }
    
    public static boolean d(Rating param1Rating) {
      return param1Rating.hasHeart();
    }
    
    public static boolean e(Rating param1Rating) {
      return param1Rating.isRated();
    }
    
    public static boolean f(Rating param1Rating) {
      return param1Rating.isThumbUp();
    }
    
    public static Rating g(boolean param1Boolean) {
      return Rating.newHeartRating(param1Boolean);
    }
    
    public static Rating h(float param1Float) {
      return Rating.newPercentageRating(param1Float);
    }
    
    public static Rating i(int param1Int, float param1Float) {
      return Rating.newStarRating(param1Int, param1Float);
    }
    
    public static Rating j(boolean param1Boolean) {
      return Rating.newThumbRating(param1Boolean);
    }
    
    public static Rating k(int param1Int) {
      return Rating.newUnratedRating(param1Int);
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\android\support\v4\media\RatingCompat.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */