package android.support.v4.media;

import android.graphics.Bitmap;
import android.media.MediaDescription;
import android.net.Uri;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.support.v4.media.session.MediaSessionCompat;

public final class MediaDescriptionCompat implements Parcelable {
  public static final Parcelable.Creator<MediaDescriptionCompat> CREATOR = new a();
  
  public final String a;
  
  public final CharSequence b;
  
  public final CharSequence c;
  
  public final CharSequence d;
  
  public final Bitmap e;
  
  public final Uri f;
  
  public final Bundle g;
  
  public final Uri h;
  
  public MediaDescription i;
  
  public MediaDescriptionCompat(String paramString, CharSequence paramCharSequence1, CharSequence paramCharSequence2, CharSequence paramCharSequence3, Bitmap paramBitmap, Uri paramUri1, Bundle paramBundle, Uri paramUri2) {
    this.a = paramString;
    this.b = paramCharSequence1;
    this.c = paramCharSequence2;
    this.d = paramCharSequence3;
    this.e = paramBitmap;
    this.f = paramUri1;
    this.g = paramBundle;
    this.h = paramUri2;
  }
  
  public static MediaDescriptionCompat c(Object paramObject) {
    MediaDescriptionCompat mediaDescriptionCompat;
    Bundle bundle = null;
    Object object = null;
    if (paramObject != null) {
      d d = new d();
      MediaDescription mediaDescription = (MediaDescription)paramObject;
      d.f(b.g(mediaDescription));
      d.i(b.i(mediaDescription));
      d.h(b.h(mediaDescription));
      d.b(b.c(mediaDescription));
      d.d(b.e(mediaDescription));
      d.e(b.f(mediaDescription));
      bundle = b.d(mediaDescription);
      paramObject = bundle;
      if (bundle != null)
        paramObject = MediaSessionCompat.n(bundle); 
      if (paramObject != null) {
        Uri uri = (Uri)paramObject.getParcelable("android.support.v4.media.description.MEDIA_URI");
      } else {
        bundle = null;
      } 
      if (bundle != null)
        if (paramObject.containsKey("android.support.v4.media.description.NULL_BUNDLE_FLAG") && paramObject.size() == 2) {
          paramObject = object;
        } else {
          paramObject.remove("android.support.v4.media.description.MEDIA_URI");
          paramObject.remove("android.support.v4.media.description.NULL_BUNDLE_FLAG");
        }  
      d.c((Bundle)paramObject);
      if (bundle != null) {
        d.g((Uri)bundle);
      } else {
        d.g(c.a(mediaDescription));
      } 
      mediaDescriptionCompat = d.a();
      mediaDescriptionCompat.i = mediaDescription;
    } 
    return mediaDescriptionCompat;
  }
  
  public CharSequence d() {
    return this.d;
  }
  
  public int describeContents() {
    return 0;
  }
  
  public Bundle g() {
    return this.g;
  }
  
  public Bitmap h() {
    return this.e;
  }
  
  public Uri i() {
    return this.f;
  }
  
  public Object j() {
    MediaDescription mediaDescription2 = this.i;
    MediaDescription mediaDescription1 = mediaDescription2;
    if (mediaDescription2 == null) {
      MediaDescription.Builder builder = b.b();
      b.n(builder, this.a);
      b.p(builder, this.b);
      b.o(builder, this.c);
      b.j(builder, this.d);
      b.l(builder, this.e);
      b.m(builder, this.f);
      b.k(builder, this.g);
      c.b(builder, this.h);
      mediaDescription1 = b.a(builder);
      this.i = mediaDescription1;
    } 
    return mediaDescription1;
  }
  
  public String k() {
    return this.a;
  }
  
  public Uri l() {
    return this.h;
  }
  
  public CharSequence m() {
    return this.c;
  }
  
  public CharSequence n() {
    return this.b;
  }
  
  public String toString() {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append(this.b);
    stringBuilder.append(", ");
    stringBuilder.append(this.c);
    stringBuilder.append(", ");
    stringBuilder.append(this.d);
    return stringBuilder.toString();
  }
  
  public void writeToParcel(Parcel paramParcel, int paramInt) {
    ((MediaDescription)j()).writeToParcel(paramParcel, paramInt);
  }
  
  public class a implements Parcelable.Creator<MediaDescriptionCompat> {
    public MediaDescriptionCompat a(Parcel param1Parcel) {
      return MediaDescriptionCompat.c(MediaDescription.CREATOR.createFromParcel(param1Parcel));
    }
    
    public MediaDescriptionCompat[] b(int param1Int) {
      return new MediaDescriptionCompat[param1Int];
    }
  }
  
  public static class b {
    public static MediaDescription a(MediaDescription.Builder param1Builder) {
      return param1Builder.build();
    }
    
    public static MediaDescription.Builder b() {
      return new MediaDescription.Builder();
    }
    
    public static CharSequence c(MediaDescription param1MediaDescription) {
      return param1MediaDescription.getDescription();
    }
    
    public static Bundle d(MediaDescription param1MediaDescription) {
      return param1MediaDescription.getExtras();
    }
    
    public static Bitmap e(MediaDescription param1MediaDescription) {
      return param1MediaDescription.getIconBitmap();
    }
    
    public static Uri f(MediaDescription param1MediaDescription) {
      return param1MediaDescription.getIconUri();
    }
    
    public static String g(MediaDescription param1MediaDescription) {
      return param1MediaDescription.getMediaId();
    }
    
    public static CharSequence h(MediaDescription param1MediaDescription) {
      return param1MediaDescription.getSubtitle();
    }
    
    public static CharSequence i(MediaDescription param1MediaDescription) {
      return param1MediaDescription.getTitle();
    }
    
    public static void j(MediaDescription.Builder param1Builder, CharSequence param1CharSequence) {
      param1Builder.setDescription(param1CharSequence);
    }
    
    public static void k(MediaDescription.Builder param1Builder, Bundle param1Bundle) {
      param1Builder.setExtras(param1Bundle);
    }
    
    public static void l(MediaDescription.Builder param1Builder, Bitmap param1Bitmap) {
      param1Builder.setIconBitmap(param1Bitmap);
    }
    
    public static void m(MediaDescription.Builder param1Builder, Uri param1Uri) {
      param1Builder.setIconUri(param1Uri);
    }
    
    public static void n(MediaDescription.Builder param1Builder, String param1String) {
      param1Builder.setMediaId(param1String);
    }
    
    public static void o(MediaDescription.Builder param1Builder, CharSequence param1CharSequence) {
      param1Builder.setSubtitle(param1CharSequence);
    }
    
    public static void p(MediaDescription.Builder param1Builder, CharSequence param1CharSequence) {
      param1Builder.setTitle(param1CharSequence);
    }
  }
  
  public static class c {
    public static Uri a(MediaDescription param1MediaDescription) {
      return param1MediaDescription.getMediaUri();
    }
    
    public static void b(MediaDescription.Builder param1Builder, Uri param1Uri) {
      param1Builder.setMediaUri(param1Uri);
    }
  }
  
  public static final class d {
    public String a;
    
    public CharSequence b;
    
    public CharSequence c;
    
    public CharSequence d;
    
    public Bitmap e;
    
    public Uri f;
    
    public Bundle g;
    
    public Uri h;
    
    public MediaDescriptionCompat a() {
      return new MediaDescriptionCompat(this.a, this.b, this.c, this.d, this.e, this.f, this.g, this.h);
    }
    
    public d b(CharSequence param1CharSequence) {
      this.d = param1CharSequence;
      return this;
    }
    
    public d c(Bundle param1Bundle) {
      this.g = param1Bundle;
      return this;
    }
    
    public d d(Bitmap param1Bitmap) {
      this.e = param1Bitmap;
      return this;
    }
    
    public d e(Uri param1Uri) {
      this.f = param1Uri;
      return this;
    }
    
    public d f(String param1String) {
      this.a = param1String;
      return this;
    }
    
    public d g(Uri param1Uri) {
      this.h = param1Uri;
      return this;
    }
    
    public d h(CharSequence param1CharSequence) {
      this.c = param1CharSequence;
      return this;
    }
    
    public d i(CharSequence param1CharSequence) {
      this.b = param1CharSequence;
      return this;
    }
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\android\support\v4\media\MediaDescriptionCompat.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */